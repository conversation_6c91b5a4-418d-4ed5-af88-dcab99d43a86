#!/bin/bash

# Workaround script to copy secrets from dev-secrets to secrets engine

source .env

echo "This script will help copy your secrets from dev-secrets to the default secrets engine"
echo ""

# First, check if secrets engine exists
echo "Checking if 'secrets' engine is mounted..."
ENGINES=$(curl -s -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/sys/mounts" | jq -r 'keys[]' | grep -E "^secrets/$")

if [ -z "$ENGINES" ]; then
    echo "The 'secrets' engine is not mounted. You'll need to mount it first:"
    echo "vault secrets enable -path=secrets kv-v2"
    exit 1
fi

echo "✓ 'secrets' engine is available"
echo ""

# Read the secret from dev-secrets
echo "Reading secret from dev-secrets/sigproject/tasklist..."
SECRET_DATA=$(curl -s -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/dev-secrets/data/sigproject/tasklist" | jq -r '.data.data')

if [ "$SECRET_DATA" == "null" ]; then
    echo "✗ Could not read secret from dev-secrets"
    exit 1
fi

echo "✓ Successfully read secret data"
echo ""

# Write to secrets engine
echo "Writing secret to secrets/sigproject/tasklist..."
curl -s -X POST \
    -H "X-Vault-Token: $VAULT_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"data\": $SECRET_DATA}" \
    "$VAULT_BASE_URL/v1/secrets/data/sigproject/tasklist" | jq .

echo ""
echo "If successful, update your app-config.yaml to use 'secrets' instead of 'dev-secrets':"
echo "vault:"
echo "  secretEngine: secrets"