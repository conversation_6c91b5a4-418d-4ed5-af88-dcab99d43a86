# This script creates a the cleanup routine in Azure.
# The routine cleans stale images within an Azure Container Registry (ACR).
# Note: Make sure to login in using AZ before running this script
# az login -t 01f7e0e8-c680-4293-8068-d572231a88f4

REGISTRY_NAME="escdev"
# Purge images older than 7 days and whose tag start with commit SHA or are untagged
PURGE_CMD="acr purge --filter backstage/.*:^[0-9a-z]{8}-?.* --ago 7d --untagged"
CRON="20 4 * * *" # Run Daily at 4:20 am

az acr task create --name purgeTask \
  --cmd "$PURGE_CMD" \
  --schedule "$CRON" \
  --registry "$REGISTRY_NAME" \
  --context /dev/null