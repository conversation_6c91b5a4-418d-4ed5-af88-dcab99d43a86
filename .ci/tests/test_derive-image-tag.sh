#!/usr/bin/env bats

setup() {
  # Prepare environment variables with default values
  export CI_DEFAULT_BRANCH="main"
  export CI_COMMIT_TAG=""
  export CUSTOM_IMAGE_TAG=""
  rm -f build.env
}

teardown() {
  unset CI_COMMIT_REF_NAME
  unset CI_DEFAULT_BRANCH
  unset CI_COMMIT_TAG
  unset CUSTOM_IMAGE_TAG
  unset CI_PIPELINE_SOURCE
  unset CI_COMMIT_SHORT_SHA
  rm -f build.env
}

@test "Pipeline triggered by git tag on default branch" {
  export CI_COMMIT_REF_NAME="main"
  export CI_COMMIT_TAG="v1.0.0"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=v1.0.0" ]
}

@test "Pipeline triggered by git tag on non default branch" {
  export CI_COMMIT_REF_NAME="feature/a"
  export CI_COMMIT_TAG="v1.0.0"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=v1.0.0-feature_a" ]
}

@test "Pipeline triggered with custom tag on main the tag ref" {
  export CI_COMMIT_REF_NAME="custom-tag"
  export CUSTOM_IMAGE_TAG="custom-tag"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=custom-tag" ]
}

@test "Pipeline triggered by git push on default branch" {
  export CI_PIPELINE_SOURCE="push"
  export CI_COMMIT_SHORT_SHA="abc123"
  export CI_COMMIT_REF_NAME="main"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=abc123" ]
}

@test "Pipeline triggered by git push on non default branch" {
  export CI_PIPELINE_SOURCE="push"
  export CI_COMMIT_SHORT_SHA="abc123"
  export CI_COMMIT_REF_NAME="feature/a"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=abc123-feature_a" ]
}

@test "Pipeline triggered from web on default branch" {
  export CI_PIPELINE_SOURCE="web"
  export CI_COMMIT_SHORT_SHA="abc123"
  export CI_COMMIT_REF_NAME="main"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=abc123" ]
}

@test "Pipeline triggered from web on non default branch" {
  export CI_PIPELINE_SOURCE="web"
  export CI_COMMIT_SHORT_SHA="abc123"
  export CI_COMMIT_REF_NAME="feature/a"
  run bash ../derive-image-tag.sh
  [ "$status" -eq 0 ]
  [ "$(cat build.env)" = "IMAGE_TAG=abc123-feature_a" ]
}

@test "Error when unable to determine BASE_IMAGE_TAG" {
  export CI_PIPELINE_SOURCE="schedule"
  export CI_COMMIT_SHORT_SHA=""
  run bash ../derive-image-tag.sh
  [ "$status" -eq 1 ]
  [ "${lines[0]}" = "Error: Unable to determine BASE_IMAGE_TAG value." ]
}