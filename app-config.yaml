app:
  title: Scaffolded Backstage App
  baseUrl: http://localhost:3000

organization:
  name: My Company

backend:
  # Used for enabling authentication, secret is shared by all backend plugins
  # See https://backstage.io/docs/auth/service-to-service-auth for
  # information on the format
  # auth:
  #   keys:
  #     - secret: ${BACKEND_SECRET}
  baseUrl: http://localhost:7007
  listen:
    port: 7007
    # Uncomment the following host directive to bind to specific interfaces
    # host: 127.0.0.1
  csp:
    connect-src: ["'self'", 'http:', 'https:']
    # Content-Security-Policy directives follow the Helmet format: https://helmetjs.github.io/#reference
    # Default Helmet Content-Security-Policy values can be removed by setting the key to false
  cors:
    origin: http://localhost:3000
    methods: [GET, HEAD, PATCH, POST, PUT, DELETE]
    credentials: true
  # This is for local development only, it is not recommended to use this in production
  # The production database configuration is stored in app-config.production.yaml
  database:
    client: better-sqlite3
    connection: ':memory:'
  # workingDirectory: /tmp # Use this to configure a working directory for the scaffolder, defaults to the OS temp-dir

permission:
  enabled: true
  rbac:
    policies-csv-file: ./default/rbac-policy.csv
    pluginsWithPermission:
      - catalog
      - scaffolder
      - permission
      - kubernetes
    admin:
      users:
        - name: group:default/backstage_system_admin
      superUsers:
        - name: group:default/backstage_system_admin

#integrations:
#  gitlab:
#    - host: gitlab.cluster.eset.corp
#      apiBaseUrl: https://gitlab.cluster.eset.corp/api/v4
#      token: ${GITLAB_TOKEN}

proxy:
  ### Example for how to add a proxy endpoint for the frontend.
  ### A typical reason to do this is to handle HTTPS and CORS for internal services.
  # endpoints:
  #   '/test':
  #     target: 'https://example.com'
  #     changeOrigin: true

# Reference documentation http://backstage.io/docs/features/techdocs/configuration
# Note: After experimenting with basic setup, use CI/CD to generate docs
# and an external cloud storage when deploying TechDocs for production use-case.
# https://backstage.io/docs/features/techdocs/how-to-guides#how-to-migrate-from-techdocs-basic-to-recommended-deployment-approach
techdocs:
  builder: 'external' # Alternatives - 'external'
  generator:
    runIn: 'docker' # Alternatives - 'local'
  publisher:
    type: 'azureBlobStorage' # Alternatives - 'googleGcs' or 'awsS3' or 'azureBlobStorage' or 'openStackSwift'. Read documentation for using alternatives.
    azureBlobStorage:
      # (Required) Azure Blob Storage Container Name
      containerName: 'docs'
      credentials:
        accountName: ${TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME}
        # (Optional) An account key is required to write to a storage container.
        # If missing,AZURE_TENANT_ID, AZURE_CLIENT_ID, AZURE_CLIENT_SECRET environment variable will be used.
        # https://docs.microsoft.com/en-us/azure/storage/common/storage-auth?toc=/azure/storage/blobs/toc.json
        accountKey: ${TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY}

auth:
  # see https://backstage.io/docs/auth/ to learn about auth providers
  providers:
    # See https://backstage.io/docs/auth/guest/provider
    guest: {}

scaffolder:
  # see https://backstage.io/docs/features/software-templates/configuration for software template options

vault:
  baseUrl: ${VAULT_BASE_URL}
  token: ${VAULT_TOKEN}
  secretEngine: ${VAULT_SECRET_ENGINE}
  kvVersion: 2

integrations:
  gitlab:
    - host: gitlab.cluster.eset.systems
      apiBaseUrl: https://gitlab.cluster.eset.systems/api/v4
      token: ${GITLAB_TOKEN}
    - host: gitlab.cluster.eset.corp
      apiBaseUrl: https://gitlab.cluster.eset.corp/api/v4
      token: ${GITLAB_TOKEN}

catalog:
  rules:
    - allow: [ Component, API, Location, Template, System, Resource ]
  providers:
    gitlab:
      gitlabClusterEsetCorp:
        host: gitlab.cluster.eset.corp # Identifies one of the hosts set up in the integrations
        branch: main # Optional. Used to discover on a specific branch
        fallbackBranch: master # Optional. Fallback to be used if there is no default branch configured at the Gitlab repository. It is only used, if `branch` is undefined. Uses `master` as default
        skipForkedRepos: false # Optional. If the project is a fork, skip repository
        includeArchivedRepos: false # Optional. If project is archived, include repository
        group: SigProject # Optional. Group and subgroup (if needed) to look for repositories. If not present the whole instance will be scanned
        entityFilename: catalog-info.yaml # Optional. Defaults to `catalog-info.yaml`
        projectPattern: '[\s\S]*' # Optional. Filters found projects based on provided patter. Defaults to `[\s\S]*`, which means to not filter anything
        excludeRepos: [ ] # Optional. A list of project paths that should be excluded from discovery, e.g. group/subgroup/repo. Should not start or end with a slash.
        schedule: # Same options as in SchedulerServiceTaskScheduleDefinition. Optional for the Legacy Backend System
          # supports cron, ISO duration, "human duration" as used in code
          frequency: { minutes: 30 }
          # supports ISO duration, "human duration" as used in code
          timeout: { minutes: 3 }
  # Overrides the default list locations from app-config.yaml as these contain example data.
  # See https://backstage.io/docs/features/software-catalog/#adding-components-to-the-catalog for more details
  # on how to get entities into the catalog.
  locations:
    # Local example data, replace this with your production config, these are intended for demo use only.
    # File locations are relative to the backend process, typically in a deployed context, such as in a Docker container, this will be the root
    - type: file
      target: ./default/groups.yaml
      rules:
        - allow: [ Group ]

    - type: file
      target: ./examples/example-entities.yaml
      rules:
        - allow: [ System, Component, API, Resource, Domain, Location ]

    - type: file
      target: ./examples/example-app.yaml
      rules:
        - allow: [ System, Component, API, Resource, Domain, Location ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/kafka-separated/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage/-/blob/main/kubernetes/clusters/deva-clusters.yaml
      rules:
        - allow: [ Resource, System, Component, API, Domain, Location ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/projects/dotNET/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/stack-gres/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/FerretDB/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/nginx-controller/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/sharded-stackgres/template.yaml
      rules:
        - allow: [ Template ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/projects/python-app/template.yaml
      rules:
        - allow: [ Template ]

    - type: file
      target: ./examples/eset-org.yaml
      rules:
        - allow: [ User, Group ]

    - type: file
      target: ./examples/cloudfield-org.yaml
      rules:
        - allow: [ User, Group ]

    - type: file
      target: ./examples/foo-org.yaml
      rules:
        - allow: [ User, Group ]

    - type: file
      target: ./examples/foo-entities.yaml
      rules:
        - allow: [ System, Component, API, Resource, Domain, Location ]

kubernetes:
  # see https://backstage.io/docs/features/kubernetes/configuration for kubernetes configuration options
