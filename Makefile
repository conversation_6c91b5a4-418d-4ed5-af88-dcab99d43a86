# Makefile for Backstage deployment

# Variables
KUBECTL := kubectl
YARN := yarn
NAMESPACE := backstage
DEPLOYMENT := backstage-app
YAML_FILE := kubernetes/kustomize/overlays/local/kustomization.yaml

# Detect OS and adjust sed command
SED_INPLACE := sed -i
uname_S := $(shell uname -s)
ifeq ($(uname_S),Darwin)
  SED_INPLACE := sed -i ''
endif


# Get the current version from newTag
CURRENT_VERSION := $(shell sed -n 's/.*newTag:[[:space:]]*\(.*\)/\1/p' $(YAML_FILE))

# Extract base version (remove timestamp suffix)
BASE_VERSION := $(shell echo $(CURRENT_VERSION) | sed 's/-.*//')

# Increment base version
NEW_BASE_VERSION := $(shell echo $(BASE_VERSION) | awk -F. '{$$NF = $$NF + 1;} 1' | sed 's/ /./g')

# Generate new version with new timestamp
TIMESTAMP := $(shell date +%Y%m%d%H%M%S)
NEW_VERSION := $(NEW_BASE_VERSION)-$(TIMESTAMP)

.PHONY: all build docker deploy portforward

all: build docker deploy portforward

build:
	@echo "Building Backstage..."
	$(YARN) clean
	$(YARN) build:backend
	@echo "Build completed. New version: $(NEW_VERSION)"

docker: build
	@echo "Building Docker image..."
	mkdir -p packages/backend/dist/skeleton packages/backend/dist/bundle
	tar xzf packages/backend/dist/skeleton.tar.gz -C packages/backend/dist/skeleton
	tar xzf packages/backend/dist/bundle.tar.gz -C packages/backend/dist/bundle

	# Create a temporary Dockerfile
	cp packages/backend/Dockerfile packages/backend/Dockerfile.temp

	# Modify the temporary Dockerfile
	$(SED_INPLACE) 's|^FROM escdev.azurecr.io/backstage/app-base.*|FROM app-base:latest|' packages/backend/Dockerfile.temp

	# Build the Docker image using the temporary Dockerfile and ensure cleanup
	docker build . --tag backstage:$(NEW_VERSION) -f packages/backend/Dockerfile.temp || { rm packages/backend/Dockerfile.temp; exit 1; }

	# Remove the temporary Dockerfile
	rm packages/backend/Dockerfile.temp

	@echo "Docker image built with tag: backstage:$(NEW_VERSION)"


deploy: docker
	@echo "Deploying to Kubernetes..."
	@$(SED_INPLACE) 's/^\([[:space:]]*newTag:[[:space:]]*\).*$$/\1$(NEW_VERSION)/' $(YAML_FILE)
	$(KUBECTL) apply -k kubernetes/kustomize/overlays/local
	@echo "Waiting for Backstage deployment to be ready..."
	@for i in $$(seq 1 10); do \
		if $(KUBECTL) rollout status deployment/$(DEPLOYMENT) -n $(NAMESPACE) --timeout=30s; then \
			echo "Backstage deployment is ready."; \
			exit 0; \
		fi; \
		echo "Deployment not ready yet. Retrying..."; \
		sleep 5; \
	done; \
	echo "Deployment failed to become ready after 10 attempts. Exiting."; \
	exit 1

portforward:
	@echo "Setting up port forwarding..."
	@echo "Backstage will be accessible at http://localhost:3000/"
	$(KUBECTL) port-forward -n $(NAMESPACE) deployment/$(DEPLOYMENT) 3000:7007

# Helper target to print the new version
print-version:
	@echo $(NEW_VERSION)
