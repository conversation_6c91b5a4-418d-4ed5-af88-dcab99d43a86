# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Backstage application (v1.35.1) - an open-source developer portal platform. It's a monorepo using Yarn workspaces with frontend (React/TypeScript) and backend (Node.js/Express) components, following a plugin-based architecture.

## Common Development Commands

### Initial Setup
```bash
yarn install          # Install dependencies
```

### Development
```bash
yarn dev             # Run frontend and backend in parallel
yarn start           # Start backend only
yarn start:backend   # Alternative backend start
```

### Build & Deploy
```bash
yarn build:backend   # Build backend
make all            # Full pipeline: build, Docker image, deploy to K8s, port forward
make build          # Build Backstage backend only
make docker         # Create Docker image (auto-versioned: X.X.X-timestamp)
make deploy         # Deploy to local Kubernetes
make portforward    # Port forward 3000:7007
```

### Testing & Quality
```bash
yarn test           # Run tests
yarn test:all       # Run all tests with coverage
yarn lint           # Run ESLint and <PERSON><PERSON><PERSON> checks
yarn lint:fix       # Auto-fix linting issues
```

### Other Commands
```bash
yarn new            # Create new Backstage components
yarn clean          # Clean build artifacts
```

## Architecture Overview

### Configuration
- **Development**: `app-config.yaml` (SQLite, local URLs)
- **Production**: `app-config.production.yaml` (PostgreSQL, Azure AD auth)
- **Kubernetes**: `/kubernetes/kustomize/` with environment-specific overlays
  - Local: `overlays/local/`
  - DEVA Hub: `overlays/deva-hub/`
  - DEVA IDP Test: `overlays/deva-idp-test/`

### Key Integrations
- **Authentication**: Azure Active Directory (production)
- **Source Control**: GitLab integration for catalog discovery
- **Infrastructure**: 
  - Kubernetes cluster management
  - ArgoCD for GitOps deployments
  - TechDocs with Azure Blob Storage backend
- **RBAC**: Community RBAC plugin with CSV-based policies (`./default/rbac-policy.csv`)

### Plugin Architecture
- Custom plugins in `/plugins/` directory
- Scaffolder backend module for timestamp actions
- Entity cards and overview pages

### Deployment
- Docker-based deployment with automatic versioning
- Kubernetes deployment using Kustomize
- Port forwarding setup: external 3000 → internal 7007
- PostgreSQL database in production (SQLite for local dev)

## Important Notes

1. **RBAC Configuration**: 
   - Admin roles: `rbac_admin`, `system_admin`
   - Groups defined in `./default/groups.yaml`
   - All groups inherit from `backstage_authenticated_user`

2. **ArgoCD Integration**: Requires service account setup in target ArgoCD instance

3. **Version Management**: The `release.sh` script and Makefile handle automatic version bumping with timestamps

4. **Local Development**: Access the app at http://localhost:3000/ after running `make portforward`

5. **Environment Variables**: Production deployments require proper secrets for database, auth tokens, and integrations