variables:
  IMAGE_NAME: "backstage/app"

stages:
  - build
  - containerize

build-backend:
  stage: build
  image: escdev.azurecr.io/backstage/builder
  script:
    - yarn install --immutable
    - yarn build:backend
  artifacts:
    paths:
      - packages/backend/dist/bundle.tar.gz
      - packages/backend/dist/skeleton.tar.gz

create-docker-auth:
  stage: containerize
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [ "" ]
  script:
    - echo "{\"auths\":{\"${ACR_NAME}\":{\"auth\":\"${DOCKER_AUTH_CONFIG}\"}}}" > config.json
  artifacts:
    paths:
      - config.json
    expire_in: 1 hour

build-container:
  stage: containerize
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [ "" ]
  needs:
    - job: build-backend
      artifacts: true
    - job: create-docker-auth
      artifacts: true
  script:
    - mv config.json /kaniko/.docker/config.json
    - mkdir packages/backend/dist/skeleton packages/backend/dist/bundle
    - tar xzf packages/backend/dist/skeleton.tar.gz -C packages/backend/dist/skeleton
    - tar xzf packages/backend/dist/bundle.tar.gz -C packages/backend/dist/bundle
    
    # Determine build behavior based on git context
    - |
      if [ -n "${CI_COMMIT_TAG}" ]; then
        # Git tag exists
        if [ "${CI_COMMIT_REF_NAME}" = "${CI_DEFAULT_BRANCH}" ]; then
          # Tagged commit on main branch - push both tag and latest
          echo "Building and pushing with git tag: ${CI_COMMIT_TAG} and latest"
          /kaniko/executor \
            --context "${CI_PROJECT_DIR}" \
            --dockerfile "${CI_PROJECT_DIR}/packages/backend/Dockerfile" \
            --destination "${ACR_NAME}/${IMAGE_NAME}:${CI_COMMIT_TAG}" \
            --destination "${ACR_NAME}/${IMAGE_NAME}:latest"
        else
          # Tagged commit on non-main branch - push only with tag
          echo "Building and pushing with git tag: ${CI_COMMIT_TAG}"
          /kaniko/executor \
            --context "${CI_PROJECT_DIR}" \
            --dockerfile "${CI_PROJECT_DIR}/packages/backend/Dockerfile" \
            --destination "${ACR_NAME}/${IMAGE_NAME}:${CI_COMMIT_TAG}"
        fi
      elif [ "${CI_COMMIT_REF_NAME}" = "${CI_DEFAULT_BRANCH}" ]; then
        # Main branch without tag - push as latest
        echo "Building and pushing from main branch as latest"
        /kaniko/executor \
          --context "${CI_PROJECT_DIR}" \
          --dockerfile "${CI_PROJECT_DIR}/packages/backend/Dockerfile" \
          --destination "${ACR_NAME}/${IMAGE_NAME}:latest"
      else
        # Other branches without tag - build only, no push
        echo "Building image without pushing (branch: ${CI_COMMIT_REF_NAME})"
        /kaniko/executor \
          --context "${CI_PROJECT_DIR}" \
          --dockerfile "${CI_PROJECT_DIR}/packages/backend/Dockerfile" \
          --no-push
      fi

