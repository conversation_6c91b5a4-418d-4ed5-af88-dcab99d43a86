---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: my-app
  description: >
    A complex system that includes multiple components, resources, and APIs interacting
    with each other to serve end users.
  labels:
    system-category: "product"
  tags:
    - backend
    - frontend
    - production
  links:
    - url: https://my-app.internal
      title: Internal Portal
      icon: web
      type: docs
spec:
  owner: group:default/ESET
  type: product  # Example classification

---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: my-app-db
  description: >
    The primary relational database storing user profiles, product data, and
    transactional records for My App.
  tags:
    - postgres
    - critical
spec:
  type: database
  owner: group:default/ESET
  system: my-app
  dependencyOf:
    - component:default/my-app-backend

---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: my-app-cache
  description: >
    An in-memory cache solution to speed up content delivery for frequent queries.
  labels:
    env: production
    vendor: redis
spec:
  type: cache
  owner: group:default/ESET
  system: my-app
  dependencyOf:
    - component:default/my-app-backend

---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: my-app-api
  description: >
    The main public-facing API for My App, handling core user and product operations.
  labels:
    version: v1
  annotations:
    billing.com/cost-center: "cc-myapp"
spec:
  type: openapi
  lifecycle: production
  owner: group:default/ESET
  system: my-app
  definition: |
    openapi: "3.0.0"
    info:
      version: 1.0.1
      title: My App API
      description: >
        Core operations for managing users, products, and orders within My App.
    servers:
      - url: https://api.myapp.org
        description: Production server
    paths:
      /users:
        get:
          summary: List users
          operationId: listUsers
          responses:
            "200":
              description: A JSON array of user objects
              content:
                application/json:
                  schema:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: "user-123"
                        name:
                          type: string
                          example: "Alice"
        post:
          summary: Create a new user
          operationId: createUser
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required: [ name ]
                  properties:
                    name:
                      type: string
                      example: "Bob"
          responses:
            "201":
              description: Created user
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "user-456"
                      name:
                        type: string
                        example: "Bob"

---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: my-app-auth-api
  description: >
    Authentication and authorization API for My App, handling tokens and roles.
spec:
  type: openapi
  lifecycle: production
  owner: group:default/ESET
  system: my-app
  definition: |
    openapi: "3.0.0"
    info:
      version: 1.1.0
      title: My App Auth API
      description: >
        Manages user authentication, access tokens, and authorization checks.
    servers:
      - url: https://auth.myapp.org
        description: Auth server
    paths:
      /token:
        post:
          summary: Obtain access token
          operationId: getAccessToken
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required: [ username, password ]
                  properties:
                    username:
                      type: string
                      example: "<EMAIL>"
                    password:
                      type: string
                      format: password
                      example: "p@ssw0rd!"
          responses:
            "200":
              description: Access token for authorized users
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      token:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            "401":
              description: Invalid credentials

---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: my-app-backend
  description: >
    The primary backend service for My App, orchestrating business logic and
    coordinating data between the database, cache, and external services.
  annotations:
    vault.io/secrets-path: sigproject
  tags:
    - nodejs
    - express
spec:
  type: service
  lifecycle: production
  owner: group:default/ESET
  system: my-app
  dependsOn:
    - resource:default/my-app-db
    - resource:default/my-app-cache
  providesApis:
    - api:default/my-app-api
    - api:default/my-app-auth-api

---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: my-app-frontend
  description: >
    The user-facing web frontend for My App, consuming core and auth APIs to
    deliver dashboards, product pages, and user controls.
  links:
    - url: https://my-app-frontend.internal/admin
      title: Admin Portal
      icon: dashboard
spec:
  type: website
  lifecycle: production
  owner: group:default/ESET
  system: my-app
  dependsOn:
    - component:default/my-app-backend
  consumesApis:
    - api:default/my-app-api
    - api:default/my-app-auth-api