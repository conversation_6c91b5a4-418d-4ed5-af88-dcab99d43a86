---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: kafka
  namespace: deva
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: postgres
  namespace: deva
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: ferretdb
  namespace: deva
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: ferretdb
  namespace: prod
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: kubernetes-ingress
  namespace: deva
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: kubernetes-ingress
  namespace: prod
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: kafka
  namespace: prod
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: postgres
  namespace: prod
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: examples
spec:
  owner: cloudfield
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: python-applications
  namespace: deva
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: python-applications
  namespace: prod
spec:
  owner: default/ESET
---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: example-website
  annotations:
    backstage.io/techdocs-ref: my-service
    backstage.io/kubernetes-id: my-service
    argocd/app-name: app-of-apps
spec:
  type: website
  lifecycle: experimental
  owner: cloudfield
  system: examples
  providesApis: [example-grpc-api]
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: example-grpc-api
spec:
  type: grpc
  lifecycle: experimental
  owner: cloudfield
  system: examples
  definition: |
    syntax = "proto3";

    service Exampler {
      rpc Example (ExampleMessage) returns (ExampleMessage) {};
    }

    message ExampleMessage {
      string example = 1;
    };
---
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: platform-team
spec:
  type: team
  children: []
---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: kubernetes-infrastructure
spec:
  owner: default/platform-team
