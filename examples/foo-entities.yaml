---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: foo-project
spec:
  owner: foo-team
---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: foo-component
  annotations:
    backstage.io/techdocs-ref: foo-component
    backstage.io/kubernetes-id: foo-component
    argocd/app-name: foo-component
spec:
  type: website
  lifecycle: experimental
  owner: foo-team
  system: foo-project
  providesApis: [foo-grpc-api]
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: foo-grpc-api
spec:
  type: grpc
  lifecycle: experimental
  owner: foo-team
  system: foo-project
  definition: |
    syntax = "proto3";

    service Exampler {
      rpc Example (ExampleMessage) returns (ExampleMessage) {};
    }

    message ExampleMessage {
      string example = 1;
    };
