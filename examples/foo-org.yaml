---
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: foo-team
spec:
  type: team
  children: [ ]
  parent: backstage_authenticated_user
---
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: foo-administrators
spec:
  type: team
  children: [ ]
  parent: backstage_authenticated_user
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: MemberFoo
spec:
  memberOf: [foo-team]
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: AdminFoo
spec:
  memberOf: [foo-team,foo-administrators]
