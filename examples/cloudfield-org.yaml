apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: Cloudfield
spec:
  type: team
  children: []
  parent: backstage_authenticated_user
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: pavel.sklenar
  annotations:
    graph.microsoft.com/user-id: 60423226-10fc-4c12-959d-abcd955bde86
spec:
  memberOf: [Cloudfield, backstage_rbac_admin, backstage_system_admin]
  profile:
    email: <EMAIL>
    displayName: <PERSON>
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: lukas.najman
  annotations:
    graph.microsoft.com/user-id: dfd52338-9ac2-4f90-87dd-12e2a6fecfe6
spec:
  memberOf: [Cloudfield, backstage_rbac_admin, backstage_system_admin]
  profile:
    email: <EMAIL>
    displayName: <PERSON><PERSON>