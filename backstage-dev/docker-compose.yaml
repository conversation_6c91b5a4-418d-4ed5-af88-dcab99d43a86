version: '3'
services:
  postgresql.backstage.dev:
    container_name: backstage-postgresql-db
    image: postgres:16
    environment:
      - POSTGRES_USER=backstage
      - POSTGRES_DB=backstage
      - POSTGRES_PASSWORD=backstage
      - PGDATA=/var/lib/postgresql/data/pgdata/
    ports:
      - '5432:5432'
    expose:
      - '5432'
    volumes:
      - './postgresql_data:/var/lib/postgresql/data'
    user: '1000:0'
    networks:
      - backstage

networks:
  backstage:
    driver: bridge