## ARGOCD
installation instructions: https://roadie.io/backstage/plugins/argo-cd/

# Auth token
To get an auth token, new user representing backstage service must be created by adding following config
into `argocd-cm` config map. This will create a user backstage and grants it access to ArgoCD API. 
```
    accounts.backstage: api<PERSON>ey
    accounts.backstage.enabled: "true"
```
Also, the user needs read access to application resources. Add it by adding the following rule under 
policy.csv in `argo-rbac-cm`. See https://argo-cd.readthedocs.io/en/stable/operator-manual/rbac/ for reference.
```
p, backstage, applications, get, *, allow
```

Auth token can be generated in UI (Settings -> Accounts -> backstage -> Generate New) or using CLI.


## RBAC
Installed RBAC modules:
* @backstage-community/plugin-rbac-backend (https://github.com/backstage/community-plugins/blob/main/workspaces/rbac/plugins/rbac-backend/README.md)
* @backstage-community/plugin-rbac (https://github.com/backstage/community-plugins/blob/main/workspaces/rbac/plugins/rbac/README.md)

### UI
@backstage-community/plugin-rbac is a APP (fronted) plugin that is providing RBAC config UI. The UI was integrated
into the Backstage UI using the steps described in plugin readme.

### Configuration
The plugin is configured through app config under the `permissions` property.
```yaml
permission:
  enabled: true
  rbac:
    policies-csv-file: ./default/rbac-policy.csv
    pluginsWithPermission:
      - catalog
      - scaffolder
      - permission
      - kubernetes
      - argocd
    admin:
      users:
        - name: group:default/backstage_system_admin
      superUsers:
        - name: group:default/backstage_system_admin
```
Two default roles are present in the system after deployment.
* role:default/rbac_admin
* role:default/system_admin

role:default/rbac_admin is provided by the plugin, and role:default/system_admin is provided through 
`default/rbac-policy.csv`. See https://github.com/backstage/community-plugins/blob/main/workspaces/rbac/plugins/rbac-backend/docs/permissions.md for more details.

Two default groups are configured in `default/groups.yaml` and has corresponding roles applied.
* backstage_rbac_admin
* backstage_system_admin

These roles are granted to users in `examples/cloudfield-org` or `examples/eset-org`.

### Group hierarchy
The RBAC plugin supports group hierarchies, see https://github.com/backstage/community-plugins/blob/main/workspaces/rbac/plugins/rbac-backend/docs/group-hierarchy.md.

All groups under the examples folder are children of the `backstage_authenticated_user` group. So any common permission policies
can be applied to this group.

The groups `backstage_rbac_admin` and `backstage_system_admin` does not have any parent group.

### Conditional Policy Aliases
Conditional policies can use aliases (variables) that are replaced during permission evaluation. 
See https://github.com/backstage/community-plugins/blob/main/workspaces/rbac/plugins/rbac-backend/docs/conditions.md#conditional-policy-aliases
