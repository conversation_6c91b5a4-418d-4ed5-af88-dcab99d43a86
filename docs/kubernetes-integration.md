# Backstage Kubernetes Integration - Current Implementation

## Overview

This document describes the current Kubernetes integration implementation in ESET's Backstage deployment, using the `deva-idp-test` cluster configuration as the reference standard.

## Table of Contents

1. [Current Architecture](#current-architecture)
2. [Authentication Configuration](#authentication-configuration)
3. [Cluster Discovery Method](#cluster-discovery-method)
4. [Reference Implementation](#reference-implementation)
5. [Configuration Files Structure](#configuration-files-structure)
6. [Security and RBAC](#security-and-rbac)
7. [Deployment Process](#deployment-process)

## Current Architecture

### High-Level Integration Architecture

```mermaid
graph LR
    subgraph "User Layer"
        U[User Browser]
    end

    subgraph "Backstage Platform"
        BF[Frontend<br/>React App]
        BB[Backend<br/>Node.js]
        KP[Kubernetes<br/>Plugin]
        CAT[Catalog<br/>Service]
    end

    subgraph "Identity Provider"
        AAD[Azure AD<br/>OIDC Provider]
    end

    subgraph "GitLab"
        GIT[Cluster<br/>Definitions]
    end

    subgraph "Kubernetes Clusters"
        API[API Server<br/>OIDC Validation]
        RBAC[RBAC<br/>Authorization]
    end

    U --> BF
    BF --> BB
    BB --> AAD
    BB --> KP
    KP --> CAT
    CAT --> GIT
    KP --> API
    API --> RBAC

    style U fill:#e1f5fe
    style AAD fill:#fff3e0
    style GIT fill:#f3e5f5
    style API fill:#e8f5e9
    style RBAC fill:#e8f5e9
```

### Detailed Technical Flow

The integration involves two distinct processes: a background catalog sync and a foreground user request flow.

#### 1. Background Process: Catalog Synchronization

This process runs periodically to discover and ingest Kubernetes cluster information into the Backstage catalog.

```mermaid
flowchart LR
    subgraph "GitLab"
        GIT_LOC["kubernetes/clusters/deva-clusters.yaml"]
        GIT_DEF["catalog/clusters/deva-idp-test.yaml"]
    end

    subgraph "Backstage"
        CAT_SVC["Catalog Service"]
        DB["Backstage Database"]
    end
    
    CAT_SVC -- "① Reads Location from app-config.yaml" --> GIT_LOC
    GIT_LOC -- "② Points to Cluster Definition" --> GIT_DEF
    CAT_SVC -- "③ Ingests Cluster Entity" --> GIT_DEF
    CAT_SVC -- "④ Stores Entity in Database" --> DB

    style GIT_LOC fill:#f3e5f5
    style GIT_DEF fill:#f3e5f5
    style CAT_SVC fill:#e3f2fd
    style DB fill:#e0f7fa
```

#### 2. Foreground Process: User Authentication and Resource Access

This is the real-time flow, from a user logging in to viewing Kubernetes resources.

```mermaid
sequenceDiagram
    participant User Browser
    participant Backstage Frontend
    participant Backstage Backend
    participant Azure AD
    participant Kubernetes API Server

    Note over User Browser, Azure AD: Authentication Flow
    User Browser->>Backstage Frontend: ① Initiate Login
    Backstage Frontend->>Backstage Backend: ② Request login redirect
    Backstage Backend->>Azure AD: ③ Request OIDC Authorization
    Azure AD-->>User Browser: ④ Redirect to Azure for login
    User Browser->>Azure AD: ⑤ User enters credentials
    Azure AD-->>Backstage Backend: ⑥ Return authorization code
    Backstage Backend->>Azure AD: ⑦ Exchange code for tokens
    Azure AD-->>Backstage Backend: ⑧ Return ID Token and Access Token
    Note over Backstage Backend: Backend creates a session<br/>and stores the tokens.
    Backstage Backend-->>Backstage Frontend: ⑨ Login successful
    Backstage Frontend-->>User Browser: ⑩ User is logged in

    Note over User Browser, Kubernetes API Server: Kubernetes Data Access Flow
    User Browser->>Backstage Frontend: ⑪ Navigate to K8s dashboard
    Backstage Frontend->>Backstage Backend: ⑫ Request Kubernetes data
    
    Note over Backstage Backend: Retrieves cluster details from Catalog DB<br/>and user's ID token from session.
    
    Backstage Backend->>Kubernetes API Server: ⑬ Proxy API request with user's OIDC ID token
    
    Kubernetes API Server->>Kubernetes API Server: ⑭ Validate OIDC token (offline)
    Kubernetes API Server->>Kubernetes API Server: ⑮ Perform RBAC check for user
    
    Kubernetes API Server-->>Backstage Backend: ⑯ Return requested resources
    Backstage Backend-->>Backstage Frontend: ⑰ Forward resources
    Backstage Frontend-->>User Browser: ⑱ Display resources in UI
```
**Note on OIDC Token Validation:** In step ⑭, the Kubernetes API server performs an *offline* validation of the JWT. It verifies the token's signature using public keys it has previously fetched and cached from the Azure AD OIDC discovery endpoint. It does not contact Azure AD for every single request, but only periodically to refresh the signing keys.

### Component Configuration Details

```mermaid
flowchart TB
    MAIN["<b>ESET Backstage Deployment</b>"]
    
    MAIN --> AUTH["<b>Authentication Layer</b>"]
    AUTH --> AUTH_CONTENT["<b>Azure AD (Microsoft Provider)</b><br/>
    • Client ID: 0322c71b-5074-4a65-b30e-72e7f57c36d7<br/>
    • Tenant ID: 01f7e0e8-c680-4293-8068-d572231a88f4<br/>
    • Email-based user mapping (emailMatchingUserEntityProfile)<br/>
    • OIDC Scopes: openid, profile, email<br/>
    • Username prefix in K8s: aad:"]
    
    MAIN --> K8S["<b>Kubernetes Plugin Configuration</b>"]
    K8S --> K8S_CONTENT["Service Locator: multiTenant<br/>
    Cluster Locator: catalog<br/>
    Auth Provider: oidc<br/>
    Token Provider: microsoft<br/>
    Skip TLS Verify: true (dev environment)<br/>
    Skip Metrics: true"]
    
    MAIN --> CAT["<b>Catalog Integration</b>"]
    CAT --> CAT_CONTENT["GitLab Repository: sigproject/backstage<br/>
    Branch: main<br/>
    Location File: /kubernetes/clusters/deva-clusters.yaml<br/>
    Cluster Definition: /catalog/clusters/deva-idp-test.yaml<br/>
    Entity Type: Resource (kind: kubernetes-cluster)<br/>
    Refresh Schedule: Every 30 minutes"]
    
    
    style MAIN fill:#f5f5f5,stroke:#333,stroke-width:2px
    style AUTH fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style AUTH_CONTENT fill:#ffffff,stroke:#1976d2,stroke-width:1px,text-align:left
    style K8S fill:#e8f5e9,stroke:#388e3c,stroke-width:2px
    style K8S_CONTENT fill:#ffffff,stroke:#388e3c,stroke-width:1px,text-align:left
    style CAT fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style CAT_CONTENT fill:#ffffff,stroke:#f57c00,stroke-width:1px,text-align:left
```

## Authentication Configuration

### Current Implementation

The system uses Azure Active Directory with OIDC tokens for Kubernetes authentication:

```yaml
# kubernetes/kustomize/overlays/deva-idp-test/config/app-config.yaml
auth:
  environment: production
  session:
    secret: ${SESSION_SECRET}
  providers:
    guest:
      userEntityRef: user:default/MemberFoo
      dangerouslyAllowOutsideDevelopment: true
    microsoft:
      production:
        clientId: ${AZURE_CLIENT_ID}
        clientSecret: ${AZURE_CLIENT_SECRET}
        tenantId: ${AZURE_TENANT_ID}
        domainHint: ${AZURE_TENANT_ID}
        additionalScopes:
          - 'openid' # For ID tokens
          - 'profile' # For user profile
          - 'email' # For email claim
        signIn:
          resolvers:
            # Email resolver for Kubernetes username mapping
            - resolver: emailMatchingUserEntityProfileEmail
```



## Cluster Discovery Method

### Catalog-Based Discovery

The current implementation uses catalog-based cluster discovery:

```yaml
# kubernetes/kustomize/overlays/deva-idp-test/config/app-config.yaml
kubernetes:
  serviceLocatorMethod:
    type: multiTenant
  clusterLocatorMethods:
    - type: catalog
```

This configuration:

- **multiTenant**: Assumes services can run on any available cluster
- **catalog**: Discovers clusters from Backstage catalog entities

## Reference Implementation

### Cluster Location Definition

```yaml
# app-config.yaml
catalog:
  locations:
    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage/-/blob/main/kubernetes/clusters/deva-clusters.yaml
      rules:
        - allow: [Resource, System, Component, API, Domain, Location]
```

### Cluster List Configuration

```yaml
# kubernetes/clusters/deva-clusters.yaml
apiVersion: backstage.io/v1alpha1
kind: Location
metadata:
  name: deva-kubernetes-clusters
  description: All Kubernetes cluster definitions
spec:
  type: url
  targets:
    - https://gitlab.cluster.eset.corp/sigproject/backstage/-/blob/main/catalog/clusters/deva-idp-test.yaml
```

### Individual Cluster Definition

```yaml
# catalog/clusters/deva-idp-test.yaml
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: idp-test
  title: 'IDP Test Cluster'
  annotations:
    kubernetes.io/api-server: 'https://api.idp-test.deva.esc.esetrs.cz:6443'
    kubernetes.io/api-server-certificate-authority: LS0tLS1CRUdJTi...
    kubernetes.io/auth-provider: 'oidc'
    kubernetes.io/oidc-token-provider: 'microsoft'
    kubernetes.io/skip-tls-verify: 'true'
    kubernetes.io/skip-metrics-lookup: 'true'
spec:
  type: kubernetes-cluster
  owner: platform-team
  system: kubernetes-infrastructure
```

### Required Annotations

Each cluster definition must include these annotations:

| Annotation                                       | Description                      | Example Value                                  |
| ------------------------------------------------ | -------------------------------- | ---------------------------------------------- |
| `kubernetes.io/api-server`                       | Kubernetes API server endpoint   | `https://api.idp-test.deva.esc.esetrs.cz:6443` |
| `kubernetes.io/api-server-certificate-authority` | Base64 encoded CA certificate    | `LS0tLS1CRUdJTi...`                            |
| `kubernetes.io/auth-provider`                    | Authentication method            | `oidc`                                         |
| `kubernetes.io/oidc-token-provider`              | OIDC provider name               | `microsoft`                                    |
| `kubernetes.io/skip-tls-verify`                  | Skip TLS verification (dev only) | `true`                                         |
| `kubernetes.io/skip-metrics-lookup`              | Skip metrics collection          | `true`                                         |

## Configuration Files Structure

```
backstage/
├── app-config.yaml                              # Main configuration with catalog location
├── kubernetes/
│   ├── clusters/
│   │   └── deva-clusters.yaml                   # Cluster location references
│   └── kustomize/
│       └── overlays/
│           └── deva-idp-test/
│               └── config/
│                   └── app-config.yaml          # Environment-specific config
└── catalog/
    └── clusters/
        └── deva-idp-test.yaml                   # Individual cluster definitions
```

## Security and RBAC

### Kubernetes API Server OIDC Configuration

The Kubernetes API server must be configured to accept Azure AD OIDC tokens:
(kubelogin does not require email claim, so preferred_username is used)

```yaml
apiServer:
  extraArgs:
    - --oidc-issuer-url=https://login.microsoftonline.com/<AZURE_TENANT_ID>/v2.0
    - --oidc-client-id=<AZURE_CLIENT_ID>
    - --oidc-username-claim=preferred_username
    - '--oidc-username-prefix=aad:'
    - --oidc-groups-claim=groups
    - '--oidc-groups-prefix=aad:'
```

### RBAC Configuration

The RBAC configuration assigns permissions to Azure AD authenticated users and groups using the `aad:` prefix.

#### Example 1: User-Specific Admin Assignment

```yaml
# kubernetes/aad-user-admin.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: aad-cluster-admin-pavel-sklenar
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - apiGroup: rbac.authorization.k8s.io
    kind: User
    name: 'aad:<EMAIL>' # Email from Azure AD with aad: prefix
```

This grants full cluster admin privileges to a specific user authenticated through Azure AD.

#### Example 2: Group-Based Read-Only Assignment

```yaml
# kubernetes/aad-group-sigproject-readonly.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: aad-group-sigproject-readonly
  namespace: sigproject
  annotations:
    description: 'Grants readonly privileges to AAD group EC-Backstage-team-sigproject'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
  - apiGroup: rbac.authorization.k8s.io
    kind: Group
    name: 'aad:d10c6a3b-143e-4e8d-b247-50c6a0196fb1' # Azure AD Group ID with aad: prefix
```

This grants read-only access to the `sigproject` namespace for all members of the Azure AD group.

#### General Backstage Viewer Role

```yaml
# ClusterRole for general Backstage users
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: backstage-viewer
rules:
  - apiGroups: ['']
    resources: ['pods', 'services', 'configmaps', 'secrets']
    verbs: ['get', 'list', 'watch']
  - apiGroups: ['apps']
    resources: ['deployments', 'replicasets', 'statefulsets']
    verbs: ['get', 'list', 'watch']
  - apiGroups: ['batch']
    resources: ['jobs', 'cronjobs']
    verbs: ['get', 'list', 'watch']
```

#### Key Points for RBAC Assignment

1. **User Assignment**: Use email address with `aad:` prefix (e.g., `aad:<EMAIL>`)
2. **Group Assignment**: Use Azure AD Group Object ID with `aad:` prefix (e.g., `aad:group-object-id`)
3. **Scope**: Can be cluster-wide (ClusterRoleBinding) or namespace-specific (RoleBinding)
4. **Roles**: Can use built-in roles (`cluster-admin`, `view`) or custom roles

## Microsoft Entra ID (Azure AD) Setup

### Prerequisites and Setup Steps

Setting up Microsoft Entra ID for Backstage Kubernetes integration requires careful configuration of the App Registration, groups, and authentication settings.

#### Step 1: Create App Registration

1. **Navigate to Azure Portal**: Go to Azure Active Directory → App registrations → New registration
2. **Configure Basic Settings**:
   - Name: `EC-Backstage` (use the exact name from your registration)
   - Supported account types: "Accounts in this organizational directory only"
   - Redirect URI: Add Web redirect URIs:
     - `https://backstage.idp-test.deva.esc.esetrs.cz/api/auth/microsoft/handler/frame`
     - `http://localhost:7007/api/auth/microsoft/handler/frame` (for development)
     - `http://localhost:8000` (for kubelogin testing)

3. **Generate Client Secret**:
   - Navigate to Certificates & secrets → New client secret
   - Store the secret value securely (needed for `AZURE_CLIENT_SECRET`)

#### Step 2: Configure API Permissions

1. **Add Required Permissions**:
   - Microsoft Graph API:
     - `openid` (Sign in and read user profile) - Delegated
     - `profile` (View users' basic profile) - Delegated
     - `email` (View users' email address) - Delegated
     - `User.Read` (Sign in and read user profile) - Delegated
     - `GroupMember.Read.All` (Read all group memberships) - Delegated

2. **Request Administrator Consent**:
   - **IMPORTANT**: On the ESET side, administrator approval is required
   - Click "Grant admin consent for [Your Organization]"
   - This step must be performed by an Azure AD administrator

#### Step 3: Configure Token Version

**Critical for kubelogin compatibility**:

1. **Navigate to App Registration Manifest**:
   - Go to your App registration → Manifest
   - Find the `accessTokenAcceptedVersion` field
   - Change from `null` or `1` to `2`:
   ```json
   "accessTokenAcceptedVersion": 2,
   ```
   - Save the manifest

This is essential because:
- kubelogin defaults to v1 tokens
- Backstage requires v2 tokens for proper Kubernetes integration
- Setting this ensures compatibility with both systems

See <https://github.com/Azure/kubelogin/issues/411>.

#### Step 4: Configure Enterprise Application Assignment

1. **Navigate to Enterprise Application**:
   - Go to Enterprise applications → Find your app registration
   - Navigate to Properties
   - Set "Assignment required?" to **Yes**
   - This enables controlled access and prevents unauthorized logins

2. **Add Groups Claim**:
   - Navigate to Token configuration → Add groups claim
   - Select token types: ID, Access, SAML
   - Select group types: "Security groups" and "Groups assigned to the application"
   - Configure to send only assigned groups, not all organization groups

#### Step 5: Create and Assign Security Groups

1. **Create Security Groups**:
   ```
   Required groups:
   - EC-Backstage-Admins (for admin access)
     Object ID: dce0b1a2-4250-4b0c-9ab7-c61e9a948699
   - EC-Backstage-team-sigproject (for read-only access)
     Object ID: d10c6a3b-143e-4e8d-b247-50c6a0196fb1
   ```

2. **Get Group Object IDs**:
   - Navigate to Azure AD → Groups
   - Find each group and copy its Object ID
   - EC-Backstage-Admins: `dce0b1a2-4250-4b0c-9ab7-c61e9a948699`
   - EC-Backstage-team-sigproject: `d10c6a3b-143e-4e8d-b247-50c6a0196fb1`

3. **Assign Users to Groups**:
   - Navigate to each group → Members → Add members
   - Add appropriate users based on their required access level

4. **Assign Groups to Enterprise Application**:
   - Navigate to Enterprise applications → Your app → Users and groups
   - Click "Add user/group"
   - Select the security groups you created
   - This ensures only group members can authenticate

#### Step 6: Configure Kubernetes RBAC

Use the group IDs obtained in Step 5 to create RBAC bindings:

1. **Group-based Read-Only Access** (`kubernetes/aad-group-sigproject-readonly.yaml`):
   ```yaml
   apiVersion: rbac.authorization.k8s.io/v1
   kind: RoleBinding
   metadata:
     name: aad-group-sigproject-readonly
     namespace: sigproject
   subjects:
   - apiGroup: rbac.authorization.k8s.io
     kind: Group
     name: "aad:d10c6a3b-143e-4e8d-b247-50c6a0196fb1"  # Use your group's Object ID
   ```

2. **User-based Admin Access** (`kubernetes/aad-user-admin.yaml`):
   ```yaml
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRoleBinding
   metadata:
     name: aad-cluster-admin-username
   subjects:
   - apiGroup: rbac.authorization.k8s.io
     kind: User
     name: "aad:<EMAIL>"  # Use the user's email
   ```

#### Step 7: Test Configuration with kubelogin

Before integrating with Backstage, verify the setup using kubelogin:

```bash
# Install kubelogin if not already installed
# macOS: brew install Azure/kubelogin/kubelogin
# Windows: choco install kubelogin
# Linux: Download from GitHub releases

# Test token acquisition
kubelogin get-token \
  --server-id 0322c71b-5074-4a65-b30e-72e7f57c36d7 \
  --client-id 0322c71b-5074-4a65-b30e-72e7f57c36d7 \
  --tenant-id 01f7e0e8-c680-4293-8068-d572231a88f4

# Expected output: Successfully acquired token
```

#### Step 8: Configure Backstage

Update your Backstage configuration with the Azure AD details:

```yaml
auth:
  providers:
    microsoft:
      production:
        clientId: 0322c71b-5074-4a65-b30e-72e7f57c36d7
        clientSecret: ${AZURE_CLIENT_SECRET}
        tenantId: 01f7e0e8-c680-4293-8068-d572231a88f4
        additionalScopes:
          - 'openid'
          - 'profile'
          - 'email'
```

### Troubleshooting Common Issues

| Issue | Solution |
|-------|----------|
| Token version mismatch | Ensure `accessTokenAcceptedVersion` is set to `2` in the App Registration manifest |
| Groups not appearing in token | Verify groups claim is added and Enterprise App is configured to send only assigned groups |
| kubelogin authentication fails | Check redirect URIs include `http://localhost:8000` for public client |
| Missing permissions | Ensure administrator consent has been granted for all API permissions |
| Users can't access Backstage | Verify Enterprise Application has "Assignment required" enabled and users/groups are assigned |
| Users can't access K8s resources | Verify users are assigned to appropriate groups and RBAC is correctly configured |

## Deployment Process

Deploying the Backstage integration involves configuring the Kubernetes cluster and the Backstage application itself.

### Step 1: Prerequisite Cluster Configuration

Before deploying Backstage, the target Kubernetes cluster must be configured correctly:

1.  **Configure OIDC on API Server**: The Kubernetes API server must be configured to trust Azure AD as an OIDC provider, as detailed in the [Security and RBAC](#security-and-rbac) section.
2.  **Apply RBAC Policies**: Create and apply the necessary `ClusterRole` and `ClusterRoleBinding` resources to grant permissions to Azure AD users and groups.

### Step 2: Backstage Kustomize Configuration

Backstage deployment is managed via Kustomize. The configuration, including secrets, is defined in the overlay.

1.  **Edit Secrets**: Navigate to `kubernetes/kustomize/overlays/deva-idp-test/kustomization.yaml`. In the `secretGenerator` section, update the literals (e.g., `AZURE_CLIENT_SECRET`, `GITLAB_TOKEN`) with the correct values for your environment.
2.  **Review ConfigMap**: The `configMapGenerator` points to the `app-config.yaml` file that holds the non-sensitive configuration for this environment.

### Step 3: Deploy Backstage

Apply the Kustomize overlay to deploy Backstage. This command builds the resources, generates the `backstage-secret` and `postgres-secret` from the `kustomization.yaml` file, and applies them to the cluster.

```bash
# Target the correct Kubernetes cluster context
kubectl config use-context <your-cluster-context>

# Apply the kustomization directory
kubectl apply -k kubernetes/kustomize/overlays/deva-idp-test
```

### Step 4: Add Cluster to Catalog

Ensure the cluster is defined as a `Resource` in the Backstage catalog via a GitOps workflow:

1.  Create a cluster definition YAML file in `catalog/clusters/`.
2.  Add a reference to this file in `kubernetes/clusters/deva-clusters.yaml`.
3.  Commit and push the changes to the GitLab repository.



## Configuration and Secrets

Backstage configuration is handled by a `ConfigMap` for non-sensitive data and a `Secret` for sensitive data. Both are generated and applied via Kustomize during the deployment.

### Configuration (`ConfigMap`)

- The main configuration is managed in `kubernetes/kustomize/overlays/deva-idp-test/config/app-config.yaml`.
- This file is used by a `configMapGenerator` in `kustomization.yaml` to create a `ConfigMap` named `backstage-config`.
- This `ConfigMap` is mounted as a volume into the Backstage pod.

### Secrets (`Secret`)

- Secrets are managed as literals within the `secretGenerator` section of `kustomization.yaml`.
- This generates a `Secret` named `backstage-secret`, which is injected into the Backstage pod as environment variables.

#### Key Secrets for Kubernetes Integration

These values are essential for the OIDC authentication with Kubernetes to function.

| Secret Key            | Description                                          |
| --------------------- | ---------------------------------------------------- |
| `AZURE_CLIENT_ID`     | The Application (client) ID for the Azure AD app.    |
| `AZURE_CLIENT_SECRET` | The client secret for the Azure AD app.              |
| `AZURE_TENANT_ID`     | The Directory (tenant) ID for the Azure AD instance. |
| `SESSION_SECRET`      | A random string used to sign user session cookies.   |

## Monitoring and Troubleshooting

### Health Checks

1. **Backstage Backend Health**:

   ```bash
   curl http://backstage-svc:7007/healthcheck
   ```

2. **Cluster Connectivity**:

   ```bash
   kubectl get --raw /api/v1/namespaces
   ```

3. **Authentication Flow**:
   - Check Azure AD logs for authentication attempts
   - Review Backstage backend logs for OIDC token issues
   - Verify Kubernetes audit logs for authorization failures

### Common Issues and Solutions

| Issue                  | Symptoms              | Solution                                        |
| ---------------------- | --------------------- | ----------------------------------------------- |
| Authentication Failed  | 401/403 errors        | Verify Azure AD configuration and OIDC settings |
| Cluster Not Found      | Empty cluster list    | Check catalog location and cluster definitions  |
| No Resources Displayed | Empty resource views  | Verify RBAC permissions for OIDC user           |
| Token Expired          | Intermittent failures | Check token refresh configuration               |

## Summary

The current Kubernetes integration implementation uses:

1. **Azure AD OIDC authentication** with email-based user mapping
2. **Catalog-based cluster discovery** from GitLab repositories
3. **Multi-tenant service location** for flexible deployment
4. **RBAC policies** based on email claims from OIDC tokens

This architecture provides:

- Centralized authentication through Azure AD
- GitOps-managed cluster definitions
- Scalable cluster discovery
- Secure, token-based access to Kubernetes resources
