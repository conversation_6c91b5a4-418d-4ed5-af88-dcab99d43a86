apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: idp-test
  title: "IDP Test Cluster"
  annotations:
    kubernetes.io/api-server: 'https://api.idp-test.deva.esc.esetrs.cz:6443'
    kubernetes.io/api-server-certificate-authority: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJSWhzbnhkSm1vLzR3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRFeU1EVXhNRE13TlRKYUZ3MHpOREV5TURNeE1ETTFOVEphTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURMMmRMOHh5aU00Si9GczgybUdwdXVsTFpOcitxVmdhNzM5b20vODJJdW5wcjg4OWNrVkZTQ2NLZ2EKUGxLYXdON3RFUDRaa29hcUN3T0htc1FWNDFCemlJRTk5dlhFaUl1b1JCWXM5bEYzMWlhR2hWZ2hGWGlyckx4TwpmRFdXOXBzMEliM0pjTDAzWnVUVGJrU0x4YmJ4NGpDYXFEZjBnS2thVERUNXlQei9ncDNGa2M4M0lHdmJUWmJaCmxDRTdhaEh0R1V6YXdUVlJSY0Y2WEhoZUt3QzEzQmlFZ3FsMitwNFVWVUhjWm54aEZXMlVwV1JzY0IxRVNhLzUKdUsvK0psUW1IY2RIWjVqNkc1amY2MlZyWDRZS1U4b3BzWjZqdWpZbml2VGEyMlNkWHZLaFNMaHlBdDFIdHpOSwp0b2JMelFqTU1wc3VLVHRKR0Vva3g2aEI5K2FKQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTNXc0V0hGaVRNYXcrRjk2dHhzeHJweGloYTVUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUp4NmhvTmdkRQoreTV0dVd3WUdkaXNPSnhLcTl1N2dHUjBJek5mdEpreTVBZDZhdTRJekM4V2xnQVUybnN6Tk0yWEl6dG1kN3R5CkRwVXBsYnFkaFg1Y1hNMy9oYm1FMkZrVWh0Y3pUanRkVE9HeDlFWllGeDFNSUVUeGp2THZ2aDU1ZDE2ZlpTZWoKNXlaYlo4dS9QNytMWWhCVkMrQUpPKzhrekN0U2tFM3FZLzFCdmVXKzhNSjFkTjdQdUpFOS9QRVlnSWRKeVBWago0bDljOVRoeXFvSWFYWm5GZWVGT2llMVRLNHdyVWl4blRTaDFsa05uRlFnOThKWm9CZ3lpcWhDbFBqSmtZM1B1CnhSMlY2M25RK3BiVHEzSUgyQnVYZVVFL21SaE1uT2sweDRFWUp4OXd1WlAxRXNZRytmcEFFblVjS2ZJc1VIblgKeHZQdDNheGRGMzZ1Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    kubernetes.io/auth-provider: 'oidc'
    kubernetes.io/oidc-token-provider: 'microsoft'
    kubernetes.io/skip-tls-verify: 'true'
    kubernetes.io/skip-metrics-lookup: 'true'
spec:
  type: kubernetes-cluster
  owner: platform-team
  system: kubernetes-infrastructure
