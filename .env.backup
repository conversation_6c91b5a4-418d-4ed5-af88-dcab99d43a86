AZURE_TENANT_ID=01f7e0e8-c680-4293-8068-d572231a88f4
AZURE_CLIENT_ID=56c9f354-75c3-4b20-921a-748867f3fbb1
AZURE_CLIENT_SECRET=****************************************
GITHUB_TOKEN=ghp_1
K8S_MINIKUBE_TOKEN=eyJhbGciOiJSUzI1NiIsImtpZCI6Im1URU12SXZWMHd1WFk4SFo4Q3Zta3hlQ2FzOThTc2JxTzZET0FuMmRUdVEifQ.eyJpc3MiOiJrdWJlcm5ldGVzL3NlcnZpY2VhY2NvdW50Iiw...
GITLAB_TOKEN=**************************
ARGOCD_URL_DAVA_HUB=https://argocd.hub.deva.esc.esetrs.cz
ARGOCD_AUTH_TOKEN_DAVA_HUB=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhcmdvY2QiLCJzdWIiOiJiYWNrc3RhZ2U6YXBpS2V5Iiw...
POSTGRES_HOST=postgres-svc
POSTGRES_PORT=5432
POSTGRES_USER=backstage
POSTGRES_PASSWORD=gWLn5vk%p*mL
NODE_EXTRA_CA_CERTS=/Users/<USER>/repos/eset/backstage/backstage-ci-build/app-base-image/certs/eset-root-ca.crt
TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME=backstagetechdocsesc
TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY="****************************************************************************************"

# Vault Configuration
VAULT_BASE_URL=https://vault.hub.deva.esc.esetrs.cz
VAULT_TOKEN=hvs.CAESILFjxfCIF71Jx_uRplVo_BENH7IQqyzgG8Kj4Sw1brcbGh4KHGh2cy5mbmxhd3R0R2xKTUphNE9kSFFpQ3hMeXc
VAULT_SECRET_ENGINE=dev-secrets
VAULT_KV_VERSION=2