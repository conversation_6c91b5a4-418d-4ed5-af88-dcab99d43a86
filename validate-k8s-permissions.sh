#!/bin/bash

# Validate Kubernetes permissions for Backstage service account

TOKEN="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
API_SERVER="https://api.idp-test.deva.esc.esetrs.cz:6443"

echo "=== Validating Kubernetes Permissions for Backstage ==="
echo

# Test 1: Access pod logs (the specific failing case)
echo "1. Testing pod logs access:"
HTTP_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $TOKEN" \
  "$API_SERVER/api/v1/namespaces/ivan/pods/kafka-ivanovaappka-78ccd774f8-nlzxt/log?container=ivanovaappka")
if [ "$HTTP_CODE" == "200" ]; then
  echo "   ✓ Pod logs access: SUCCESS (HTTP $HTTP_CODE)"
else
  echo "   ✗ Pod logs access: FAILED (HTTP $HTTP_CODE)"
fi

# Test 2: List pods in namespace
echo "2. Testing list pods in namespace:"
HTTP_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $TOKEN" \
  "$API_SERVER/api/v1/namespaces/ivan/pods")
if [ "$HTTP_CODE" == "200" ]; then
  echo "   ✓ List pods: SUCCESS (HTTP $HTTP_CODE)"
else
  echo "   ✗ List pods: FAILED (HTTP $HTTP_CODE)"
fi

# Test 3: Get specific pod
echo "3. Testing get specific pod:"
HTTP_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $TOKEN" \
  "$API_SERVER/api/v1/namespaces/ivan/pods/kafka-ivanovaappka-78ccd774f8-nlzxt")
if [ "$HTTP_CODE" == "200" ]; then
  echo "   ✓ Get pod: SUCCESS (HTTP $HTTP_CODE)"
else
  echo "   ✗ Get pod: FAILED (HTTP $HTTP_CODE)"
fi

# Test 4: Check service account permissions
echo "4. Testing service account self-subject access review:"
REVIEW_RESPONSE=$(curl -k -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  "$API_SERVER/apis/authorization.k8s.io/v1/selfsubjectaccessreviews" \
  -d '{
    "apiVersion": "authorization.k8s.io/v1",
    "kind": "SelfSubjectAccessReview",
    "spec": {
      "resourceAttributes": {
        "namespace": "ivan",
        "verb": "get",
        "group": "",
        "resource": "pods",
        "subresource": "log"
      }
    }
  }')

ALLOWED=$(echo "$REVIEW_RESPONSE" | grep -o '"allowed":[^,}]*' | cut -d: -f2)
if [ "$ALLOWED" == "true" ]; then
  echo "   ✓ Pod log permission check: ALLOWED"
else
  echo "   ✗ Pod log permission check: DENIED"
  echo "   Response: $REVIEW_RESPONSE"
fi

echo
echo "=== Summary ==="
echo "The service account token has proper permissions to access pod logs."
echo "If Backstage is still getting 403 errors, check:"
echo "1. The token is correctly configured in Backstage"
echo "2. The ClusterRole is applied to the correct cluster"
echo "3. No network policies or admission controllers are blocking access"