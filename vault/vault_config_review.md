# Vault OIDC Configuration - Code Review

## 1. Overall Assessment

This project is well-structured and demonstrates a solid understanding of Vault OIDC configuration management. The two-phase "generate and deploy" approach is a best practice, separating configuration templating from the deployment process. The use of `Taskfile.yml` and a comprehensive `README.md` makes the project accessible and easy to manage.

The following review focuses on enhancing security, improving the robustness of the shell scripts, and increasing the long-term maintainability of the system. The suggestions are designed to be fully compatible with the current workflow.

---

## 2. High-Priority Security Concerns

### 2.1. Secrets in Generated Files and CI Artifacts

**Observation:**
The `parse-projects.sh` script uses `envsubst` to inject variables, including `AAD_CLIENT_SECRET`, directly into the `generated/oidc-config.json` file. The `.gitlab-ci.yml` pipeline then saves the entire `generated/` directory as a job artifact.

**Risk:**
This is a significant security risk. GitLab CI artifacts are not encrypted by default and can be downloaded by any user with Developer permissions (or higher) on the project. This exposes the `AAD_CLIENT_SECRET` in plain text.

**Recommendation:**
Modify the process to ensure secrets are only handled during the deployment phase and never stored in intermediate files.

1.  **Modify `parse-projects.sh`:**
    *   In the `generate_oidc_config` function, explicitly *exclude* the `oidc_client_secret` from the generated `oidc-config.json` file. The template should still contain the `${AAD_CLIENT_SECRET}` variable.

2.  **Modify `setup-vault-oidc.sh`:**
    *   In the `configure_oidc` function, which already uses `envsubst` to create a temporary config file, the `${AAD_CLIENT_SECRET}` variable will be correctly substituted at the last possible moment, using the secure environment variable from the CI runner.

3.  **Modify `.gitlab-ci.yml`:**
    *   The `generate-vault-configs` job will now produce artifacts that do not contain secrets, making them safe to store.
    *   The `deploy-to-vault` job will consume these artifacts and use its own environment variables (from GitLab CI secrets) to inject the `AAD_CLIENT_SECRET` during the `vault write` command.

---

## 3. Shell Scripting Improvements

The core logic in `parse-projects.sh` and `setup-vault-oidc.sh` is functional but can be made more robust and maintainable.

### 3.1. Enhance Script Robustness

**Observation:**
The scripts use `set -e`, which is good. However, they are missing other important safety settings.

**Recommendation:**
Add `set -u` and `set -o pipefail` to the top of both shell scripts, just below `#!/bin/bash`.

```bash
#!/bin/bash
set -euo pipefail
```

*   `set -u`: Treats unset variables as an error, preventing unpredictable behavior from empty values.
*   `set -o pipefail`: Ensures that a pipeline command fails if any command in the pipeline fails, not just the last one.

### 3.2. Use `jq` for Robust JSON Generation

**Observation:**
The `parse-projects.sh` script constructs JSON files using heredocs and `sed`. This is fragile and can easily break if values contain special characters.

**Recommendation:**
Refactor the JSON generation functions to use `jq`, which is already a dependency in the GitLab CI environment. This makes the process safer and more readable.

**Example (`generate_project_role`):**

```bash
# Instead of this:
cat > "$temp_template" << EOF
{
  "user_claim": "$user_claim",
  ...
  "policies": ["default", "$project_name"],
  ...
}
EOF
envsubst < "$temp_template" > "$output_file"

# Use jq to build the JSON object:
jq -n \
  --arg user_claim "$user_claim" \
  --argjson allowed_redirect_uris "$allowed_redirect_uris" \
  --arg token_ttl "$token_ttl" \
  --arg project_name "$project_name" \
  '{
    user_claim: $user_claim,
    allowed_redirect_uris: $allowed_redirect_uris,
    policies: ["default", $project_name],
    token_ttl: $token_ttl,
    ...
  }' > "$output_file"
```

### 3.3. Safer Temporary File Creation

**Observation:**
The scripts create temporary files in `/tmp` with predictable names (e.g., `/tmp/role-${project_name}-template-${RANDOM}.json`).

**Recommendation:**
Use the `mktemp` command to create secure temporary files or directories. This prevents potential race conditions and file collisions.

```bash
# Instead of this:
temp_file="/tmp/role-${role_name}-${RANDOM}.json"

# Use this:
temp_file=$(mktemp)
# Or with a template:
temp_file=$(mktemp "/tmp/role-${role_name}.XXXXXX.json")

# And ensure cleanup, even on script exit:
trap 'rm -f "$temp_file"' EXIT
```

---

## 4. Configuration and Template Enhancements

### 4.1. Dynamic Redirect URIs

**Observation:**
The `projects.yml` and `parse-projects.sh` script assume a maximum of two redirect URIs (`VAULT_REDIRECT_URI_1`, `VAULT_REDIRECT_URI_2`). This is not scalable.

**Recommendation:**
Modify the scripts and templates to handle a dynamic number of URIs.

1.  **`parse-projects.sh` (`load_env`):** Instead of creating numbered variables, create a JSON array from the space-separated string.
    ```bash
    # In load_env function
    if [[ -n "${VAULT_REDIRECT_URIS:-}" ]]; then
        export REDIRECT_URIS_JSON=$(echo "$VAULT_REDIRECT_URIS" | jq -R 'split(" ")' | jq -c '.')
    fi
    ```

2.  **Templates (`role-*.json.template`):** Use the new JSON variable.
    ```json
    "allowed_redirect_uris": ${REDIRECT_URIS_JSON},
    ```
    `envsubst` will substitute this correctly.

### 4.2. Explicit Token TTLs

**Observation:**
The generated `role-sigproject.json` has empty strings for `token_ttl` and `token_max_ttl`. While Vault falls back to the system/mount default, being explicit improves clarity and avoids unexpected behavior if global defaults change.

**Recommendation:**
In `parse-projects.sh`, ensure that the `yq` expression provides a fallback to the default values defined in `projects.yml` if the project-specific values are null or empty. The current `//` operator in `yq` already does this, but you should verify that the environment variables (e.g., `${SIGPROJECT_TOKEN_TTL}`) are not being set to an empty string, which would override the default.

A safer approach in `generate_project_role` would be:
```bash
token_ttl=$(yq eval ".projects.${project_name}.token_ttl // .defaults.role.token_ttl" "$PROJECTS_FILE")
# Add a check in case the env var is empty
if [[ -z "$token_ttl" ]]; then
  token_ttl=$(yq eval '.defaults.role.token_ttl' "$PROJECTS_FILE")
fi
```

---

## 5. GitLab CI Improvements

### 5.1. Use `rules` instead of `only`

**Observation:**
The `deploy-to-vault` job uses the `only` keyword, which is being deprecated.

**Recommendation:**
Convert the `only` block to a `rules` block for future compatibility.

```yaml
deploy-to-vault:
  # ...
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
      changes:
        - generated/**/*
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
      when: manual
      allow_failure: false
```

### 5.2. Pinning vs. Hardcoding Versions

**Observation:**
The Vault CLI version (`1.15.2`) is hardcoded in the `deploy-to-vault` job.

**Recommendation:**
Move the version number to the `variables` section at the top of the `.gitlab-ci.yml` file. This makes it easier to find and update.

```yaml
variables:
  VAULT_VERSION: "1.15.2"

deploy-to-vault:
  # ...
  before_script:
    - wget -O vault.zip https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip
    # ...
```

This review provides a roadmap for enhancing your already impressive Vault configuration system. By addressing the security concerns and implementing the suggested robustness improvements, you can ensure the project remains secure, scalable, and easy to maintain.
