version: '3'

# Simplified Vault OIDC Management
# Two-phase approach: Generate configurations, then deploy to Vault

tasks:
  # Core Tasks
  setup:
    desc: Generate configurations and deploy to Vault (with cleanup)
    cmds:
      - ./parse-projects.sh generate-all
      - ./setup-vault-oidc.sh

  deploy:
    desc: Deploy generated configurations to Vault (with cleanup)
    cmds:
      - ./setup-vault-oidc.sh

  deploy-no-cleanup:
    desc: Deploy generated configurations without cleaning up unused items
    cmds:
      - ./setup-vault-oidc.sh --no-cleanup

  dry-run:
    desc: Show what would be deployed without making changes
    cmds:
      - ./setup-vault-oidc.sh --dry-run

  # Project Management
  list-projects:
    desc: List all configured projects and base roles
    cmds:
      - ./parse-projects.sh list

  validate-projects:
    desc: Validate projects.yml configuration
    cmds:
      - ./parse-projects.sh validate

  generate-all:
    desc: Generate all configurations (OIDC + policies + roles)
    cmds:
      - ./parse-projects.sh generate-all

  # Utility Tasks
  show-generated:
    desc: Show generated folder structure
    cmds:
      - |
        if [ -d "generated" ]; then
          echo "📁 Generated folder structure:"
          find generated -type f | sort | sed 's/^/  /'
          echo ""
          echo "📊 Summary:"
          echo "  Policies: $(find generated/policies -name "*.hcl" 2>/dev/null | wc -l)"
          echo "  Roles: $(find generated/roles -name "*.json" 2>/dev/null | wc -l)"
          echo "  OIDC Config: $([ -f generated/oidc-config.json ] && echo "✅" || echo "❌")"
        else
          echo "❌ Generated folder not found. Run 'task generate-all' first."
        fi

  clean:
    desc: Clean up temporary files and generated folder
    cmds:
      - rm -f /tmp/*-role*.json /tmp/oidc-config*.json /tmp/policy-*.hcl
      - rm -rf generated/
      - echo "✅ Cleaned up temporary files and generated folder"

  help:
    desc: Show available tasks
    cmds:
      - |
        echo "🔧 Simplified Vault OIDC Management"
        echo "=================================="
        echo ""
        echo "Core Tasks:"
        echo "  task setup             - Generate configurations and deploy to Vault (with cleanup)"
        echo "  task deploy            - Deploy generated configurations to Vault (with cleanup)"
        echo "  task deploy-no-cleanup - Deploy without cleaning up unused items"
        echo "  task dry-run           - Show what would be deployed"
        echo ""
        echo "Project Management:"
        echo "  task list-projects    - List all configured projects and base roles"
        echo "  task validate-projects - Validate projects.yml configuration"
        echo "  task generate-all     - Generate all configurations"
        echo ""
        echo "Utility:"
        echo "  task show-generated   - Show generated folder structure"
        echo "  task clean           - Clean up temporary files and generated folder"
        echo "  task help            - Show this help"
        echo ""
        echo "Examples:"
        echo "  task setup           # Full setup (generate + deploy)"
        echo "  task dry-run         # Preview deployment"
        echo "  task list-projects   # Show configured projects"