# Default policy for all authenticated users
# Provides minimal access - login success only, no secrets access

# Allow users to look up their own token information
path "auth/token/lookup-self" {
  capabilities = ["read"]
}

# Allow users to renew their own tokens
path "auth/token/renew-self" {
  capabilities = ["update"]
}

# Allow users to revoke their own tokens (logout)
path "auth/token/revoke-self" {
  capabilities = ["update"]
}

# Allow access to their own token metadata
path "auth/token/lookup" {
  capabilities = ["update"]
}

# Allow users to list auth methods (for UI functionality)
path "sys/auth" {
  capabilities = ["read"]
}

# Allow users to read system health (for UI functionality)
path "sys/health" {
  capabilities = ["read"]
}

# Deny access to all secrets by default
# Specific policies will grant access to specific paths
path "secret/*" {
  capabilities = ["deny"]
}
