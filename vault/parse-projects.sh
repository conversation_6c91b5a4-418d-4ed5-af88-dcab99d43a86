#!/bin/bash

# Project Configuration Parser for Vault OIDC
# This script parses projects.yml and generates policies and roles dynamically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="${SCRIPT_DIR}/config"
GENERATED_DIR="${SCRIPT_DIR}/generated"
PROJECTS_FILE="${CONFIG_DIR}/projects.yml"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if yq is available
check_yq() {
    if ! command -v yq &> /dev/null; then
        print_error "yq is required but not installed"
        print_info "Install with: brew install yq (macOS) or apt-get install yq (Ubuntu)"
        exit 1
    fi
}

# Function to load environment variables
load_env() {
    local env_file="${SCRIPT_DIR}/.env.vault"

    if [[ ! -f "$env_file" ]]; then
        print_error "Environment file not found: $env_file"
        print_info "Please copy .env.vault.template to .env.vault and fill in the values"
        exit 1
    fi

    print_info "Loading environment variables from $env_file"

    # Source the env file
    set -a
    source "$env_file"
    set +a

    # Parse redirect URIs into array if they exist
    if [[ -n "${VAULT_REDIRECT_URIS:-}" ]]; then
        IFS=' ' read -ra REDIRECT_URI_ARRAY <<< "$VAULT_REDIRECT_URIS"
        export VAULT_REDIRECT_URI_1="${REDIRECT_URI_ARRAY[0]:-}"
        export VAULT_REDIRECT_URI_2="${REDIRECT_URI_ARRAY[1]:-}"
    fi

    print_success "Environment variables loaded successfully"
}

# Function to validate projects.yml
validate_projects_file() {
    if [[ ! -f "$PROJECTS_FILE" ]]; then
        print_error "Projects file not found: $PROJECTS_FILE"
        exit 1
    fi
    
    print_info "Validating projects configuration..."
    
    # Check if file is valid YAML
    if ! yq eval '.' "$PROJECTS_FILE" > /dev/null 2>&1; then
        print_error "Invalid YAML in $PROJECTS_FILE"
        exit 1
    fi
    
    print_success "Projects configuration is valid"
}

# Function to get list of projects
get_projects() {
    yq eval '.projects | keys | .[]' "$PROJECTS_FILE"
}

# Function to get project configuration
get_project_config() {
    local project_name="$1"
    yq eval ".projects.${project_name}" "$PROJECTS_FILE"
}

# Function to get default configuration
get_defaults() {
    yq eval '.defaults' "$PROJECTS_FILE"
}

# Function to get base roles
get_base_roles() {
    yq eval '.base_roles | keys | .[]' "$PROJECTS_FILE"
}

# Function to get base role configuration
get_base_role_config() {
    local role_name="$1"
    yq eval ".base_roles.${role_name}" "$PROJECTS_FILE"
}

# Function to generate policy for a project
generate_project_policy() {
    local project_name="$1"
    local output_file="$2"
    
    print_info "Generating policy for project: $project_name"
    
    # Get project-specific policy template or use default
    local policy_template
    policy_template=$(yq eval ".projects.${project_name}.policy_template // .defaults.policy_template" "$PROJECTS_FILE")
    
    # Replace template variables
    echo "$policy_template" | sed "s/{{\.project_name}}/${project_name}/g" > "$output_file"
    
    print_success "Policy generated: $output_file"
}

# Function to generate role for a project
generate_project_role() {
    local project_name="$1"
    local output_file="$2"
    
    print_info "Generating role for project: $project_name"
    
    # Get project configuration
    local group_id
    group_id=$(yq eval ".projects.${project_name}.group_id" "$PROJECTS_FILE")
    
    if [[ "$group_id" == "null" ]]; then
        print_error "Missing mandatory field 'group_id' for project: $project_name"
        exit 1
    fi
    
    # Get default role configuration
    local user_claim bound_audiences allowed_redirect_uris groups_claim oidc_scopes
    user_claim=$(yq eval '.defaults.role.user_claim' "$PROJECTS_FILE")
    bound_audiences=$(yq eval '.defaults.role.bound_audiences' "$PROJECTS_FILE")
    allowed_redirect_uris=$(yq eval '.defaults.role.allowed_redirect_uris' "$PROJECTS_FILE")
    groups_claim=$(yq eval '.defaults.role.groups_claim' "$PROJECTS_FILE")
    oidc_scopes=$(yq eval '.defaults.role.oidc_scopes' "$PROJECTS_FILE")
    
    # Get project-specific or default TTL settings
    local token_ttl token_max_ttl
    token_ttl=$(yq eval ".projects.${project_name}.token_ttl // .defaults.role.token_ttl" "$PROJECTS_FILE")
    token_max_ttl=$(yq eval ".projects.${project_name}.token_max_ttl // .defaults.role.token_max_ttl" "$PROJECTS_FILE")
    
    # Create temporary template with variables
    local temp_template="/tmp/role-${project_name}-template-${RANDOM}.json"
    cat > "$temp_template" << EOF
{
  "user_claim": "$user_claim",
  "bound_audiences": "$bound_audiences",
  "allowed_redirect_uris": $allowed_redirect_uris,
  "groups_claim": "$groups_claim",
  "oidc_scopes": "$oidc_scopes",
  "policies": ["default", "$project_name"],
  "token_ttl": "$token_ttl",
  "token_max_ttl": "$token_max_ttl",
  "bound_claims": {
    "groups": ["$group_id"]
  }
}
EOF

    # Substitute environment variables and generate final role
    envsubst < "$temp_template" > "$output_file"

    # Clean up temp file
    rm -f "$temp_template"
    
    print_success "Role generated: $output_file"
}

# Function to generate base role configuration
generate_base_role() {
    local role_name="$1"
    local output_file="$2"

    print_info "Generating base role: $role_name"

    # Get base role configuration
    local template_file policies
    template_file=$(yq eval ".base_roles.${role_name}.template_file" "$PROJECTS_FILE")
    policies=$(yq eval ".base_roles.${role_name}.policies" "$PROJECTS_FILE")

    if [[ "$template_file" == "null" ]]; then
        print_error "Missing template_file for base role: $role_name"
        exit 1
    fi

    # Check if template file exists
    local template_path="${CONFIG_DIR}/${template_file}"
    if [[ ! -f "$template_path" ]]; then
        print_error "Template file not found: $template_path"
        exit 1
    fi

    # Create temporary file with policy substitution
    local temp_file="/tmp/role-${role_name}-${RANDOM}.json"

    # First substitute environment variables
    envsubst < "$template_path" > "$temp_file"

    # Then substitute the policies from projects.yml
    if [[ "$policies" != "null" ]]; then
        # Convert YAML array to JSON array format and replace using jq
        local policies_json
        policies_json=$(echo "$policies" | yq eval -o=json '.')

        # Replace the policies array in the JSON using jq
        jq --argjson policies "$policies_json" '.policies = $policies' "$temp_file" > "$output_file"
    else
        cp "$temp_file" "$output_file"
    fi

    # Clean up temp file
    rm -f "$temp_file"

    print_success "Base role generated: $output_file"
}

# Function to substitute environment variables excluding secrets
envsubst_no_secrets() {
    local input_file="$1"
    local output_file="$2"

    # For now, let's use a simpler approach - substitute everything
    # The setup script will handle secrets properly during deployment
    envsubst < "$input_file" > "$output_file"

    print_warning "Note: Secrets are included in generated files. Ensure proper access control."
}

# Function to generate OIDC configuration
generate_oidc_config() {
    local output_file="$1"

    print_info "Generating OIDC configuration (excluding secrets)"

    local oidc_template="${CONFIG_DIR}/oidc-config.json.template"
    if [[ ! -f "$oidc_template" ]]; then
        print_error "OIDC template not found: $oidc_template"
        exit 1
    fi

    # Substitute environment variables (except secrets)
    # Secrets will be handled by setup script for security
    envsubst_no_secrets "$oidc_template" "$output_file"

    print_success "OIDC configuration generated: $output_file (secrets excluded)"
}

# Function to generate identity group for a role/project
generate_identity_group() {
    local group_name="$1"
    local output_file="$2"
    local policies="$3"
    
    print_info "Generating identity group: $group_name"
    
    # Get template
    local template_file="${CONFIG_DIR}/identity-group.json.template"
    if [[ ! -f "$template_file" ]]; then
        print_error "Identity group template not found: $template_file"
        exit 1
    fi
    
    # Create temp file with environment substitution
    local temp_file="/tmp/identity-group-${group_name}-${RANDOM}.json"
    
    # Set template variables for envsubst
    export GROUP_NAME="$group_name"
    export POLICIES_JSON="$policies"
    
    # Apply template
    envsubst < "$template_file" > "$temp_file"
    
    # Copy to final location  
    cp "$temp_file" "$output_file"
    
    # Clean up
    rm -f "$temp_file"
    unset GROUP_NAME POLICIES_JSON
    
    print_success "Identity group generated: $output_file"
}

# Function to generate group alias
generate_group_alias() {
    local aad_group_id="$1"
    local group_name="$2"
    local output_file="$3"
    
    print_info "Generating group alias for: $group_name (AAD: $aad_group_id)"
    
    # Get template
    local template_file="${CONFIG_DIR}/group-alias.json.template"
    if [[ ! -f "$template_file" ]]; then
        print_error "Group alias template not found: $template_file"
        exit 1
    fi
    
    # Create temp file with environment substitution  
    local temp_file="/tmp/group-alias-${group_name}-${RANDOM}.json"
    
    # Set template variables for envsubst
    export AAD_GROUP_ID="$aad_group_id"
    export MOUNT_ACCESSOR="\${MOUNT_ACCESSOR}"  # Escape for runtime substitution
    export CANONICAL_ID="\${CANONICAL_ID}"      # Escape for runtime substitution
    
    # Apply template
    envsubst < "$template_file" > "$temp_file"
    
    # Copy to final location
    cp "$temp_file" "$output_file"
    
    # Clean up
    unset AAD_GROUP_ID MOUNT_ACCESSOR CANONICAL_ID
    
    # Clean up
    rm -f "$temp_file"
    
    print_success "Group alias generated: $output_file"
}

# Function to generate all identity groups and aliases
generate_identity_groups() {
    local output_dir="$1"
    
    print_info "Generating identity groups and aliases..."
    
    # Create identity-groups directory
    mkdir -p "$output_dir/identity-groups"
    
    # Generate identity groups for base roles that have group_id
    while IFS= read -r role; do
        if [[ -n "$role" ]]; then
            # Check if base role has a group_id defined in environment
            local group_id_var="AAD_$(echo "$role" | tr '[:lower:]' '[:upper:]')_GROUP_ID"
            local group_id="${!group_id_var}"
            
            if [[ -n "$group_id" && "$group_id" != "" ]]; then
                # Get policies for this base role
                local policies
                policies=$(yq eval ".base_roles.${role}.policies" "$PROJECTS_FILE" | yq eval -o=json '.')
                
                # Generate identity group
                generate_identity_group "$role" "${output_dir}/identity-groups/group-${role}.json" "$policies"
                
                # Generate group alias
                generate_group_alias "$group_id" "$role" "${output_dir}/identity-groups/alias-${role}.json"
            fi
        fi
    done < <(get_base_roles)
    
    # Generate identity groups for each project
    while IFS= read -r project; do
        if [[ -n "$project" ]]; then
            # Get project group ID
            local group_id
            group_id=$(yq eval ".projects.${project}.group_id" "$PROJECTS_FILE")
            
            if [[ "$group_id" != "null" && -n "$group_id" ]]; then
                # Substitute environment variables in group_id if needed
                group_id=$(echo "$group_id" | envsubst)
                
                # Generate identity group with default and project-specific policies
                local policies='["default", "'$project'"]'
                generate_identity_group "$project" "${output_dir}/identity-groups/group-${project}.json" "$policies"
                
                # Generate group alias
                generate_group_alias "$group_id" "$project" "${output_dir}/identity-groups/alias-${project}.json"
            fi
        fi
    done < <(get_projects)
    
    print_success "Identity groups and aliases generated"
}

# Function to generate all configurations
generate_all_projects() {
    local output_dir="${1:-${GENERATED_DIR}}"

    print_info "Generating all configurations to: $output_dir"

    # Create output directory structure
    mkdir -p "$output_dir/roles" "$output_dir/policies" "$output_dir/identity-groups"

    # Generate OIDC configuration in root
    generate_oidc_config "${output_dir}/oidc-config.json"

    # Copy and substitute static policies
    for policy_file in "$CONFIG_DIR"/policy-*.hcl; do
        if [[ -f "$policy_file" ]]; then
            local policy_name
            policy_name=$(basename "$policy_file")
            envsubst < "$policy_file" > "${output_dir}/policies/${policy_name}"
            print_success "Static policy processed: $policy_name"
        fi
    done

    # Generate base roles
    while IFS= read -r role; do
        if [[ -n "$role" ]]; then
            generate_base_role "$role" "${output_dir}/roles/role-${role}.json"
        fi
    done < <(get_base_roles)

    # Generate configurations for each project
    while IFS= read -r project; do
        if [[ -n "$project" ]]; then
            generate_project_policy "$project" "${output_dir}/policies/policy-${project}.hcl"
            generate_project_role "$project" "${output_dir}/roles/role-${project}.json"
        fi
    done < <(get_projects)

    # Generate identity groups and aliases
    generate_identity_groups "$output_dir"

    print_success "All configurations generated (OIDC + policies + roles + identity groups)"
}

# Function to list projects
list_projects() {
    print_info "Available base roles:"
    while IFS= read -r role; do
        if [[ -n "$role" ]]; then
            local template_file policies
            template_file=$(yq eval ".base_roles.${role}.template_file" "$PROJECTS_FILE")
            policies=$(yq eval ".base_roles.${role}.policies[]" "$PROJECTS_FILE" | tr '\n' ',' | sed 's/,$//')
            echo "  - $role (Template: $template_file, Policies: $policies)"
        fi
    done < <(get_base_roles)

    echo
    print_info "Available projects:"
    while IFS= read -r project; do
        if [[ -n "$project" ]]; then
            local group_id
            group_id=$(yq eval ".projects.${project}.group_id" "$PROJECTS_FILE")
            echo "  - $project (Group: $group_id)"
        fi
    done < <(get_projects)
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo
    echo "Commands:"
    echo "  list                    List all configured base roles and projects"
    echo "  generate [PROJECT]      Generate policy and role for specific project"
    echo "  generate-base [ROLE]    Generate configuration for specific base role"
    echo "  generate-all [DIR]      Generate all configurations (default: current dir)"
    echo "  validate               Validate projects.yml configuration"
    echo
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 generate sigproject"
    echo "  $0 generate-base admin"
    echo "  $0 generate-all /tmp/vault-configs"
    echo "  $0 validate"
}

# Main execution
main() {
    check_yq
    validate_projects_file

    # Load environment variables for generation commands
    case "${1:-}" in
        "generate"|"generate-base"|"generate-all")
            load_env
            ;;
    esac
    
    case "${1:-}" in
        "list")
            list_projects
            ;;
        "generate")
            if [[ -z "${2:-}" ]]; then
                print_error "Project name required for generate command"
                show_usage
                exit 1
            fi
            local project="$2"
            mkdir -p "policies" "roles"
            generate_project_policy "$project" "policies/policy-${project}.hcl"
            generate_project_role "$project" "roles/role-${project}.json"
            ;;
        "generate-base")
            if [[ -z "${2:-}" ]]; then
                print_error "Base role name required for generate-base command"
                show_usage
                exit 1
            fi
            local role="$2"
            mkdir -p "roles"
            generate_base_role "$role" "roles/role-${role}.json"
            ;;
        "generate-all")
            generate_all_projects "${2:-}"
            ;;
        "validate")
            print_success "Configuration is valid"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        "")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
