# Vault OIDC Configuration Environment Variables
# Filled with actual values from wiki/vault configuration

# Vault Configuration
VAULT_ADDR=https://vault-teams.idp-test.deva.esc.esetrs.cz
VAULT_TOKEN=hvs.vCmVjPaxu0f9LpEoELptvLvJ

# Azure AD OIDC Configuration
AAD_CLIENT_ID=6fdba74d-3e64-4f49-a5e7-988e0f1893cf
AAD_CLIENT_SECRET=****************************************
AAD_TENANT_ID=01f7e0e8-c680-4293-8068-d572231a88f4

# Vault OIDC Settings
OIDC_DEFAULT_ROLE=azure_users
OIDC_DISCOVERY_URL=https://login.microsoftonline.com/01f7e0e8-c680-4293-8068-d572231a88f4/v2.0
OIDC_SCOPES="https://graph.microsoft.com/.default profile"

# Redirect URIs (space-separated)
VAULT_REDIRECT_URIS="https://vault-teams.idp-test.deva.esc.esetrs.cz/ui/vault/auth/oidc/oidc/callback http://localhost:8250/oidc/callback"

# Azure AD Groups (Group Object IDs from Azure AD)
# Note: Only two groups are currently defined in Azure AD:
# - EC-Vault-Teams-Admins: 529780cb-6cc0-4feb-ba62-db482d9e5432 (Admin access)
# - EC-Vault-Team-sigproject: 47584b32-b297-4505-879d-1cf679dde88a (Project/Developer access)
AAD_ADMIN_GROUP_ID=529780cb-6cc0-4feb-ba62-db482d9e5432
AAD_SIGPROJECT_GROUP_ID=47584b32-b297-4505-879d-1cf679dde88a
AAD_READONLY_GROUP_ID=47584b32-b297-4505-879d-1cf679dde88a

# Token Settings
ADMIN_TOKEN_TTL=4h
ADMIN_TOKEN_MAX_TTL=12h
DEVELOPER_TOKEN_TTL=8h
DEVELOPER_TOKEN_MAX_TTL=24h
READONLY_TOKEN_TTL=8h
READONLY_TOKEN_MAX_TTL=24h
# Default token settings for azure_users
DEFAULT_TOKEN_TTL=8h

# KV Secrets Engine Path
KV_PATH=secret