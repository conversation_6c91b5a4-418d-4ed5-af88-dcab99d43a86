# Dynamic Project Configuration for Vault OIDC
# This file defines all projects and their Vault access configurations
#
# Each project can have:
# - Mandatory fields: name, group_id
# - Optional fields: policy_template, role_template, custom settings
# - Default values will be used if not specified

# Default configuration applied to all projects unless overridden
defaults:
  # Default role configuration
  role:
    user_claim: "sub"
    bound_audiences: "${AAD_CLIENT_ID}"
    allowed_redirect_uris: ["${VAULT_REDIRECT_URI_1}", "${VAULT_REDIRECT_URI_2}"]
    groups_claim: "groups"
    oidc_scopes: "${OIDC_SCOPES}"
    token_ttl: "8h"
    token_max_ttl: "24h"
  
  # Default policy template (can be overridden per project)
  # Note: Default policy provides login capabilities, so project policies only need to define secret access
  policy_template: |
    # Full access to project-specific secrets (KV v2)
    path "secret/data/{{.project_name}}/*" {
      capabilities = ["create", "read", "update", "delete", "list"]
    }
    path "secret/metadata/{{.project_name}}/*" {
      capabilities = ["read", "list", "delete"]
    }

    # List access at the root level for navigation
    path "secret/metadata" {
      capabilities = ["list"]
    }

# Project definitions
projects:
  # Example: sigproject
  sigproject:
    # Mandatory: Azure AD group ID for this project
    group_id: "${AAD_SIGPROJECT_GROUP_ID}"
    
    # Optional: Custom token TTL settings
    token_ttl: "${SIGPROJECT_TOKEN_TTL}"
    token_max_ttl: "${SIGPROJECT_TOKEN_MAX_TTL}"
    
    # Optional: Custom policy (if not specified, uses default template)
    # policy_template: |
    #   # Custom policy for sigproject
    #   path "secret/data/sigproject/*" {
    #     capabilities = ["create", "read", "update", "delete", "list"]
    #   }
    #   # Add more custom paths here
    
    # Optional: Additional role settings
    # role_overrides:
    #   token_ttl: "12h"
    #   additional_policies: ["shared-resources"]

  # Example: Another project with custom policy
  # myproject:
  #   group_id: "${AAD_MYPROJECT_GROUP_ID}"
  #   
  #   # Custom policy with additional paths
  #   policy_template: |
  #     # Project-specific secrets
  #     path "secret/data/myproject/*" {
  #       capabilities = ["create", "read", "update", "delete", "list"]
  #     }
  #     path "secret/metadata/myproject/*" {
  #       capabilities = ["read", "list", "delete"]
  #     }
  #     # Additional custom path
  #     path "secret/data/shared/myproject-resources/*" {
  #       capabilities = ["read", "list"]
  #     }
  #     # Standard read access
  #     path "secret/data/*" {
  #       capabilities = ["read"]
  #     }
  #     path "secret/metadata/*" {
  #       capabilities = ["list"]
  #     }
  #   
  #   # Custom role settings
  #   role_overrides:
  #     token_ttl: "4h"
  #     token_max_ttl: "12h"

# Base roles (non-project specific)
base_roles:
  # Default role for OIDC login
  default:
    template_file: "role-default.json.template"
    policies: ["default"]


  # Admin role - gets both default and admin policies
  admin:
    template_file: "role-admin.json.template"
    policies: ["default", "admin"]


