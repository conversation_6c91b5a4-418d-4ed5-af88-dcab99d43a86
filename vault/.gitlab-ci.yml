# GitLab CI Pipeline for Vault OIDC Configuration Repository
# Two-phase approach: Generate configurations, then deploy to Vault

stages:
  - generate
  - deploy

variables:
  # Pipeline uses .env.vault defaults with environment variable overrides
  # Only secrets need to be set as GitLab CI/CD variables for security

# Generate configurations when vault configuration changes (excluding generated/)
generate-vault-configs:
  stage: generate
  image: alpine:latest
  before_script:
    - apk add --no-cache bash yq jq gettext
  script:
    - echo "Generating Vault configurations using .env.vault defaults..."
    - echo "Pipeline variables can override any .env.vault values"
    - ./parse-projects.sh generate-all
    - echo "Generated configurations:"
    - find generated -type f -name "*.json" -o -name "*.hcl" | sort
  artifacts:
    paths:
      - generated/
    expire_in: 1 hour
  rules:
    # Run when configuration changes, but exclude changes to generated/ folder
    - if: '$CI_PIPELINE_SOURCE == "push"'
      changes:
        - "**/*"
        - "!generated/**/*"
    # Also run manually
    - when: manual
      allow_failure: false
  after_script:
    - echo "Generated $(find generated -type f | wc -l) files at $(date)"

# Deploy to Vault when generated/ folder changes or manually triggered
deploy-to-vault:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache bash curl jq gettext
    # Install Vault CLI
    - wget -O vault.zip https://releases.hashicorp.com/vault/1.15.2/vault_1.15.2_linux_amd64.zip
    - unzip vault.zip
    - mv vault /usr/local/bin/
    - chmod +x /usr/local/bin/vault
  script:
    - echo "Deploying Vault configurations using .env.vault defaults..."
    - echo "Pipeline variables override .env.vault values"
    # Validate required secret environment variables (must be set in pipeline)
    - |
      if [ -z "$VAULT_TOKEN" ]; then
        echo "ERROR: VAULT_TOKEN must be set as GitLab CI/CD variable"
        exit 1
      fi
      if [ -z "$AAD_CLIENT_SECRET" ]; then
        echo "ERROR: AAD_CLIENT_SECRET must be set as GitLab CI/CD variable"
        exit 1
      fi
    # Test Vault connectivity
    - vault status
    # Deploy configurations (script handles env override automatically)
    - ./setup-vault-oidc.sh
  rules:
    # Run when generated/ folder changes
    - changes:
        - generated/**/*
    # Also run manually (but require generate job artifacts)
    - when: manual
      allow_failure: false
  dependencies:
    - generate-vault-configs
  environment:
    name: vault-production
    url: $VAULT_ADDR
  # Only run in protected branches for security
  only:
    - main
    - master
    - develop

# Validation job - runs on all changes to validate configuration
validate-config:
  stage: generate
  image: alpine:latest
  before_script:
    - apk add --no-cache bash yq
  script:
    - echo "Validating Vault configuration..."
    - ./parse-projects.sh validate
    - echo "✅ Configuration is valid"
  rules:
    - changes:
        - "**/*"
    - when: manual
      allow_failure: true

# Dry-run job - shows what would be deployed without making changes
dry-run-deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache bash curl jq gettext
    - wget -O vault.zip https://releases.hashicorp.com/vault/1.15.2/vault_1.15.2_linux_amd64.zip
    - unzip vault.zip
    - mv vault /usr/local/bin/
    - chmod +x /usr/local/bin/vault
  script:
    - echo "Dry-run deployment (no changes will be made)..."
    - ./setup-vault-oidc.sh --dry-run
  rules:
    - when: manual
      allow_failure: true
  dependencies:
    - generate-vault-configs
  environment:
    name: vault-production
    url: $VAULT_ADDR
    action: prepare
