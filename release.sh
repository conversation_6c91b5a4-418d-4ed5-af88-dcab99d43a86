#!/bin/bash
# <PERSON>ript to automate the release process: tagging, updating kustomization files, committing, and pushing.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
KUSTOMIZE_FILE_DEVA_HUB="kubernetes/kustomize/overlays/deva-hub/kustomization.yaml"
KUSTOMIZE_FILE_DEVA_IDP_TEST="kubernetes/kustomize/overlays/deva-idp-test/kustomization.yaml"
IMAGE_BASE_NAME="escdev.azurecr.io/backstage/app"

# --- Colors ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# --- Helper Functions ---
info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error_exit() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
    exit 1
}

ask_yes_no() {
    local prompt_message="$1"
    while true; do
        # Changed prompt to (Y/n) to indicate Y is default
        read -r -p "$(echo -e "${YELLOW}${prompt_message} (Y/n): ${NC}")" yn
        # If user presses Enter, default to Yes
        if [ -z "$yn" ]; then
            return 0
        fi
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo -e "${RED}Please answer yes (Y) or no (n).${NC}";;
        esac
    done
}

# --- Pre-flight checks ---
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    error_exit "Not a git repository. Please run this script from the root of a git repository."
fi

if ! command -v git &> /dev/null; then
    error_exit "git command could not be found. Please install git."
fi

# --- 1. Determine Next Git Tag ---
info "Determining the next Git tag..."
LATEST_TAG=$(git tag --sort=version:refname | grep -E '^v?[0-9]+\.[0-9]+\.[0-9]+$' | tail -n 1)
NEW_VERSION=""
SHOULD_CREATE_TAG=true # Default to true, will be set to false if 'use_latest_tag' is chosen

if [ -z "$LATEST_TAG" ]; then
    warn "No existing valid semver tags (vX.Y.Z or X.Y.Z) found."
    initial_prefix_v="v" 
    if ask_yes_no "Do you want to start with version ${initial_prefix_v}0.1.0?"; then
        NEW_VERSION="${initial_prefix_v}0.1.0"
    else
        read -r -p "$(echo -e "${YELLOW}Enter the initial version (e.g., v0.1.0 or 0.1.0): ${NC}")" USER_INITIAL_VERSION
        if [[ ! "$USER_INITIAL_VERSION" =~ ^v?[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            error_exit "Invalid version format. Please use format like vX.Y.Z or X.Y.Z."
        fi
        NEW_VERSION="$USER_INITIAL_VERSION"
    fi
    # SHOULD_CREATE_TAG remains true as we are defining a new version
else
    info "Latest valid semver tag found: $LATEST_TAG"
    
    LATEST_TAG_PREFIX_V=""
    LATEST_TAG_VERSION_PART=""

    if [[ "$LATEST_TAG" =~ ^v ]]; then
        LATEST_TAG_PREFIX_V="v"
        LATEST_TAG_VERSION_PART="${LATEST_TAG#v}"
    else
        LATEST_TAG_PREFIX_V=""
        LATEST_TAG_VERSION_PART="$LATEST_TAG"
    fi

    if [[ ! "$LATEST_TAG_VERSION_PART" =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
        error_exit "Internal error: Could not parse latest tag components from '$LATEST_TAG_VERSION_PART'."
    fi
    CURRENT_MAJOR=${BASH_REMATCH[1]}
    CURRENT_MINOR=${BASH_REMATCH[2]}
    CURRENT_PATCH=${BASH_REMATCH[3]}

    echo -e "${YELLOW}Latest tag is $LATEST_TAG. Current parsed version: $LATEST_TAG_PREFIX_V$CURRENT_MAJOR.$CURRENT_MINOR.$CURRENT_PATCH. Choose an action:${NC}"
    # Reordered options to make "patch" the first (default)
    options=("patch" "minor" "major" "use_latest_tag ($LATEST_TAG)" "cancel")
    PS3="$(echo -e "${YELLOW}Select an option (default is 1. patch): ${NC}")"
    
    COLUMNS=1 # Ensure select menu is presented clearly
    # Print options with numbers
    echo -e "${YELLOW}Select a version bump action:${NC}"
    for idx in "${!options[@]}"; do
        num=$((idx + 1))
        echo "  $num) ${options[$idx]}"
    done

    while true; do
        read -r -p "$(echo -e "${YELLOW}Select an option (default is 1. patch): ${NC}")" user_choice
        actual_choice=""
        # Enter pressed: default to option 1 ("patch")
        if [ -z "$user_choice" ]; then
            actual_choice="${options[0]}"
        # Single digit 1-5
        elif [[ "$user_choice" =~ ^[1-5]$ ]]; then
            actual_choice="${options[$((user_choice-1))]}"
        # Accept also option name directly (patch/minor/major/...)
        else
            matched=false
            for idx in "${!options[@]}"; do
                opt="${options[$idx]}"
                # For use_latest_tag, accept either exact match or "use_latest_tag"
                if [[ "$user_choice" == "$opt" ]] || \
                   { [[ "$opt" == use_latest_tag* ]] && [[ "$user_choice" =~ ^use_latest_tag ]]; }; then
                    actual_choice="$opt"
                    matched=true
                    break
                fi
            done
            if [ "$matched" = false ]; then
                echo -e "${RED}Invalid input: '$user_choice'. Please select 1-${#options[@]} or an option name (patch, minor, major, use_latest_tag, cancel).${NC}"
                continue
            fi
        fi

        case "$actual_choice" in
            patch)
                MAJOR=$CURRENT_MAJOR
                MINOR=$CURRENT_MINOR
                PATCH=$((CURRENT_PATCH + 1))
                NEW_VERSION="$LATEST_TAG_PREFIX_V$MAJOR.$MINOR.$PATCH"
                SHOULD_CREATE_TAG=true
                break
                ;;
            minor)
                MAJOR=$CURRENT_MAJOR
                MINOR=$((CURRENT_MINOR + 1))
                PATCH=0
                NEW_VERSION="$LATEST_TAG_PREFIX_V$MAJOR.$MINOR.$PATCH"
                SHOULD_CREATE_TAG=true
                break
                ;;
            major)
                MAJOR=$((CURRENT_MAJOR + 1))
                MINOR=0
                PATCH=0
                NEW_VERSION="$LATEST_TAG_PREFIX_V$MAJOR.$MINOR.$PATCH"
                SHOULD_CREATE_TAG=true
                break
                ;;
            "use_latest_tag ($LATEST_TAG)")
                NEW_VERSION="$LATEST_TAG"
                SHOULD_CREATE_TAG=false
                info "Will use existing tag $NEW_VERSION for file updates. No new tag will be created."
                break
                ;;
            cancel)
                info "Operation cancelled by user."
                exit 0
                ;;
            *)
                echo -e "${RED}Invalid option: '$user_choice'. Please select a number from 1 to ${#options[@]} or an option name.${NC}"
                ;;
        esac
    done
fi

if [ "$SHOULD_CREATE_TAG" = true ]; then
    info "New version for tag will be: $NEW_VERSION"
    if ! ask_yes_no "Proceed with creating new tag '$NEW_VERSION' after file updates?"; then
        error_exit "Tag creation cancelled. Aborting."
    fi
    # Tag creation moved to after commit
else
    info "Using existing version $NEW_VERSION for file updates. Tag creation skipped."
    if ! ask_yes_no "Proceed with updating files using existing version '$NEW_VERSION'?"; then
        info "File update process cancelled."
        exit 0
    fi
fi

# --- 2. Update Kustomization Files ---
FILES_TO_ADD=()
UPDATED_A_FILE=false
NEW_VERSION_WITH_PREFIX="$NEW_VERSION" # Full version (with or without 'v' prefix, as chosen/tagged)

update_kustomization_file() {
    local file_path="$1"
    
    if [ ! -f "$file_path" ]; then
        warn "Kustomization file not found: $file_path. Skipping."
        return
    fi

    info "Updating $file_path to image version $NEW_VERSION_WITH_PREFIX..."
    
    # Create a temporary file
    tmp_file=$(mktemp)
    
    # Flag to track if we found the image path line
    found_path=0
    # Flag to track if we updated the file
    updated=0
    
    # Process the file line by line
    while IFS= read -r line; do
        if [ $found_path -eq 1 ]; then
            # We're at the line after the image path, which should be the value line
            if [[ "$line" =~ value:.*${IMAGE_BASE_NAME}: ]]; then
                # This is the image value line, replace it with proper indentation
                echo "        value: ${IMAGE_BASE_NAME}:${NEW_VERSION_WITH_PREFIX}" >> "$tmp_file"
                updated=1
                found_path=0  # Reset flag
            else
                # If the next line isn't a value line, just write it and reset flag
                echo "$line" >> "$tmp_file"
                found_path=0
            fi
        elif [[ "$line" =~ path:\ */spec/template/spec/containers/0/image ]]; then
            # Found the image path line, write it and set flag to process next line
            echo "$line" >> "$tmp_file"
            found_path=1
        else
            # Regular line, just write it
            echo "$line" >> "$tmp_file"
        fi
    done < "$file_path"
    
    if [ $updated -eq 0 ]; then
        warn "No image version line found to update in $file_path."
        rm "$tmp_file"
        return 1
    fi
    
    # Verify the change before moving
    if ! grep -q "        value: ${IMAGE_BASE_NAME}:${NEW_VERSION_WITH_PREFIX}" "$tmp_file"; then
        warn "Update verification failed for $file_path. The version string was not found after processing."
        warn "Expected: '        value: ${IMAGE_BASE_NAME}:${NEW_VERSION_WITH_PREFIX}'"
        warn "File produced:"
        cat "$tmp_file"
        rm "$tmp_file"
        if ask_yes_no "File processing produced unexpected output for $file_path. Continue without updating this file?"; then
            return 1 # Indicate failure to update this specific file
        else
            error_exit "Update of $file_path aborted by user due to verification failure."
        fi
    fi
    
    # Move the temporary file to replace the original
    if ! mv "$tmp_file" "$file_path"; then
        error_exit "Failed to move temporary file to $file_path."
    fi

    success "Successfully updated $file_path."
    FILES_TO_ADD+=("$file_path")
    UPDATED_A_FILE=true
    return 0
}

if ask_yes_no "Update version in '$KUSTOMIZE_FILE_DEVA_HUB'?"; then
    update_kustomization_file "$KUSTOMIZE_FILE_DEVA_HUB"
fi

if ask_yes_no "Update version in '$KUSTOMIZE_FILE_DEVA_IDP_TEST'?"; then
    update_kustomization_file "$KUSTOMIZE_FILE_DEVA_IDP_TEST"
fi

# --- 3. Git Add ---
if [ "$UPDATED_A_FILE" = true ] && [ ${#FILES_TO_ADD[@]} -gt 0 ]; then
    info "Adding updated kustomization files to git index..."
    git add "${FILES_TO_ADD[@]}"
    success "Files added to git index: ${FILES_TO_ADD[*]}"
else
    info "No kustomization files were updated or selected for update that required adding to git."
fi

# --- 4. Git Commit ---
# Check if there are staged changes (either from kustomization updates or other manual staging)
COMMIT_WAS_MADE=false
if ! git diff --cached --quiet || { [ "$UPDATED_A_FILE" = true ] && [ ${#FILES_TO_ADD[@]} -gt 0 ]; }; then
    if ask_yes_no "Do you want to commit the staged changes?"; then
        # Adjust commit message slightly if using an existing tag
        if [ "$SHOULD_CREATE_TAG" = true ]; then
            DEFAULT_COMMIT_MESSAGE="chore: bump version to $NEW_VERSION and update files"
        else
            DEFAULT_COMMIT_MESSAGE="chore: update files to align with version $NEW_VERSION"
        fi
        read -r -p "$(echo -e "${YELLOW}Enter commit message (default: '$DEFAULT_COMMIT_MESSAGE'): ${NC}")" COMMIT_MESSAGE
        COMMIT_MESSAGE="${COMMIT_MESSAGE:-$DEFAULT_COMMIT_MESSAGE}"
        
        info "Committing changes with message: '$COMMIT_MESSAGE'..."
        git commit -m "$COMMIT_MESSAGE"
        success "Changes committed."
        COMMIT_WAS_MADE=true
    else
        info "Skipping commit. Staged changes (if any) remain."
    fi
else
    info "No files were updated and staged by this script, or no other staged changes detected. Skipping commit step."
fi

# --- 4.5. Create Tag After Commit ---
if [ "$SHOULD_CREATE_TAG" = true ]; then
    info "Creating tag $NEW_VERSION on the latest commit..."
    if git tag "$NEW_VERSION"; then
        success "Successfully tagged with $NEW_VERSION."
    else
        error_exit "Failed to create tag $NEW_VERSION. It might already exist or another git error occurred."
    fi
fi

# --- 5. Git Push ---
TAG_WAS_PUSHED=false # Flag to track if the tag was actually pushed in this run
if ask_yes_no "Do you want to push the commit (if any) and potentially the tag '$NEW_VERSION' to remote 'origin'?"; then
    info "Pushing current branch to origin..."
    git push origin HEAD 
    success "Branch pushed."
    
    if [ "$SHOULD_CREATE_TAG" = true ]; then # Only push tag if it was newly created in this run
        info "Pushing newly created tag $NEW_VERSION to origin..."
        git push origin "$NEW_VERSION"
        success "Tag $NEW_VERSION pushed."
        TAG_WAS_PUSHED=true
    else
        info "Tag $NEW_VERSION was an existing tag or not created in this run. Skipping explicit push for the tag itself."
    fi
else
    info "Skipping push. Remember to push manually if needed:"
    info "  git push origin HEAD"
    if [ "$SHOULD_CREATE_TAG" = true ]; then # Remind to push tag only if it was meant to be new
        info "  git push origin $NEW_VERSION"
    fi
fi

if [ "$SHOULD_CREATE_TAG" = true ]; then
    if [ "$TAG_WAS_PUSHED" = true ]; then
        success "Release process finished for new version $NEW_VERSION (tag created and pushed)!"
    else
        success "Release process finished for new version $NEW_VERSION (tag created, not pushed)!"
    fi
else
    success "File update process finished using existing version $NEW_VERSION!"
fi

