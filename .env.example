# Azure Configuration
AZURE_TENANT_ID=00000000-0000-0000-0000-000000000000
AZURE_CLIENT_ID=00000000-0000-0000-0000-000000000000
AZURE_CLIENT_SECRET=xxxxxxx~xXXXXxxxxxXXXXxxxxxxXXXXXXxxxxx

# Authentication Tokens
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITLAB_TOKEN=glpat-xxxxxxxxxxxxxxxxx

# Kubernetes Tokens
K8S_MINIKUBE_TOKEN=eyJhbGciOiJSUzI1NiIsImtpZCI6IlhYWFhYIn0.xxxxxxxxxxxxx...

# ArgoCD Configuration
ARGOCD_URL_DAVA_HUB=https://argocd.hub.deva.esc.esetrs.cz
ARGOCD_AUTH_TOKEN_DAVA_HUB=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.xxxxxxxxxxxx...

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=backstage
POSTGRES_PASSWORD=xxxxxxxxxx

# Path to CA Certificate
NODE_EXTRA_CA_CERTS=/path/to/your/ca-certificate.crt

# TechDocs Storage
TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME=techdocsstore
TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Vault Configuration
VAULT_BASE_URL=https://vault.hub.deva.esc.esetrs.cz
VAULT_TOKEN=hvs.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
VAULT_SECRET_ENGINE=dev-secrets
VAULT_KV_VERSION=2

# Backstage Configuration
BACKSTAGE_BASE_URL=http://localhost:3000