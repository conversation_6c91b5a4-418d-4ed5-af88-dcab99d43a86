{"name": "backend", "version": "0.0.0", "main": "dist/index.cjs.js", "types": "src/index.ts", "private": true, "backstage": {"role": "backend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "build-image": "docker build ../.. -f <PERSON><PERSON><PERSON><PERSON> --tag backstage"}, "dependencies": {"@backstage-community/plugin-rbac-backend": "^5.2.10", "@backstage-community/plugin-vault-backend": "^0.9.0", "@backstage/backend-common": "^0.25.0", "@backstage/backend-defaults": "^0.7.0", "@backstage/config": "^1.3.2", "@backstage/plugin-app-backend": "^0.4.4", "@backstage/plugin-auth-backend": "^0.24.2", "@backstage/plugin-auth-backend-module-github-provider": "^0.2.4", "@backstage/plugin-auth-backend-module-guest-provider": "^0.2.4", "@backstage/plugin-auth-backend-module-microsoft-provider": "^0.2.4", "@backstage/plugin-auth-node": "^0.5.6", "@backstage/plugin-catalog-backend": "^1.30.0", "@backstage/plugin-catalog-backend-module-gitlab": "^0.6.2", "@backstage/plugin-catalog-backend-module-logs": "^0.1.6", "@backstage/plugin-catalog-backend-module-scaffolder-entity-model": "^0.2.4", "@backstage/plugin-kubernetes-backend": "^0.19.2", "@backstage/plugin-permission-backend": "^0.5.53", "@backstage/plugin-permission-backend-module-allow-all-policy": "^0.2.4", "@backstage/plugin-permission-common": "^0.8.4", "@backstage/plugin-permission-node": "^0.8.7", "@backstage/plugin-proxy-backend": "^0.5.10", "@backstage/plugin-scaffolder-backend": "^1.29.0", "@backstage/plugin-scaffolder-backend-module-gitlab": "^0.8.0-next.2", "@backstage/plugin-search-backend": "^1.8.1", "@backstage/plugin-search-backend-module-catalog": "^0.3.0", "@backstage/plugin-search-backend-module-pg": "^0.5.40", "@backstage/plugin-search-backend-module-techdocs": "^0.3.5", "@backstage/plugin-search-backend-node": "^1.3.7", "@backstage/plugin-techdocs-backend": "^1.11.5", "@immobiliarelabs/backstage-plugin-gitlab-backend": "^6.10.0", "@roadiehq/**********************cd-backend": "^3.3.1", "app": "link:../app", "node-gyp": "^10.0.0", "pg": "^8.11.3"}, "devDependencies": {"@backstage/cli": "^0.29.6", "better-sqlite3": "^9.0.0"}, "files": ["dist"]}