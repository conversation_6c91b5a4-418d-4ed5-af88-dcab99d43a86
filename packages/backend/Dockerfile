FROM escdev.azurecr.io/backstage/app-base:latest

# Copy the install dependencies from the build stage and context
COPY --chown=node:node .yarn ./.yarn
COPY --chown=node:node .yarnrc.yml  ./
COPY --chown=node:node yarn.lock package.json packages/backend/dist/skeleton/ ./
# Note: The skeleton bundle only includes package.json files -- if your app has
# plugins that define a `bin` export, the bin files need to be copied as well to
# be linked in node_modules/.bin during yarn install.

RUN yarn workspaces focus --all --production

# Copy the built packages from the build stage
COPY --chown=node:node packages/backend/dist/bundle ./

# Copy any other files that we need at runtime
RUN mkdir configuration \
    && touch configuration/app-config.yaml
COPY --chown=node:node app-config.yaml configuration/app-config.base.yaml


# This will include the examples, if you don't need these simply remove this line
COPY --chown=node:node examples ./examples
COPY --chown=node:node default ./default

# This switches many Node.js dependencies to production mode.
ENV NODE_ENV=production

# This disables node snapshot for Node 20 to work with the Scaffolder
ENV NODE_OPTIONS="--use-openssl-ca --no-node-snapshot"

# Copy entrypoint with proper permissions
COPY --chown=node:node --chmod=755 entrypoint.sh ./entrypoint.sh
ENTRYPOINT ["./entrypoint.sh", "node", "packages/backend"]

CMD ["--config", "configuration/app-config.base.yaml", "--config", "configuration/app-config.yaml"]