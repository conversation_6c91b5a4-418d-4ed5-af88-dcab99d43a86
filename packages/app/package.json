{"name": "app", "version": "0.0.0", "private": true, "bundled": true, "backstage": {"role": "frontend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "clean": "backstage-cli package clean", "test": "backstage-cli package test", "lint": "backstage-cli package lint"}, "dependencies": {"@backstage-community/plugin-rbac": "^1.33.6", "@backstage-community/plugin-vault": "^0.6.0", "@backstage/app-defaults": "^1.5.16", "@backstage/catalog-model": "^1.7.3", "@backstage/cli": "^0.29.6", "@backstage/core-app-api": "^1.15.4", "@backstage/core-components": "^0.16.3", "@backstage/core-plugin-api": "^1.10.3", "@backstage/integration-react": "^1.2.3", "@backstage/plugin-api-docs": "^0.12.3", "@backstage/plugin-catalog": "^1.26.1", "@backstage/plugin-catalog-common": "^1.1.3", "@backstage/plugin-catalog-graph": "^0.4.15", "@backstage/plugin-catalog-import": "^0.12.9", "@backstage/plugin-catalog-react": "^1.15.1", "@backstage/plugin-kubernetes": "^0.12.3", "@backstage/plugin-org": "^0.6.35", "@backstage/plugin-permission-react": "^0.4.30", "@backstage/plugin-scaffolder": "^1.27.5", "@backstage/plugin-search": "^1.4.22", "@backstage/plugin-search-react": "^1.8.5", "@backstage/plugin-techdocs": "^1.12.2", "@backstage/plugin-techdocs-module-addons-contrib": "^1.1.20", "@backstage/plugin-techdocs-react": "^1.2.13", "@backstage/plugin-user-settings": "^0.8.18", "@backstage/theme": "^0.6.3", "@immobiliarelabs/backstage-plugin-gitlab": "^6.10.0", "@material-ui/core": "^4.12.2", "@material-ui/icons": "^4.9.1", "@roadiehq/backstage-plugin-argo-cd": "^2.8.6", "react": "^18.0.2", "react-dom": "^18.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@backstage/test-utils": "^1.7.4", "@playwright/test": "^1.32.3", "@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react-dom": "*", "cross-env": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "files": ["dist"]}