#!/bin/bash

# Test script for Vault integration
# Usage: ./test-vault.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "=== Vault Integration Test Script ==="
echo ""

# Load environment variables
if [ -f .env ]; then
    source .env
    echo -e "${GREEN}✓ Loaded .env file${NC}"
else
    echo -e "${RED}✗ .env file not found${NC}"
    exit 1
fi

# Check required variables
echo ""
echo "=== Checking Environment Variables ==="
if [ -z "$VAULT_BASE_URL" ]; then
    echo -e "${RED}✗ VAULT_BASE_URL is not set${NC}"
    exit 1
else
    echo -e "${GREEN}✓ VAULT_BASE_URL: $VAULT_BASE_URL${NC}"
fi

if [ -z "$VAULT_TOKEN" ]; then
    echo -e "${RED}✗ VAULT_TOKEN is not set${NC}"
    exit 1
else
    echo -e "${GREEN}✓ VAULT_TOKEN: ${VAULT_TOKEN:0:10}...${NC}"
fi

if [ -z "$VAULT_SECRET_ENGINE" ]; then
    echo -e "${RED}✗ VAULT_SECRET_ENGINE is not set${NC}"
    exit 1
else
    echo -e "${GREEN}✓ VAULT_SECRET_ENGINE: $VAULT_SECRET_ENGINE${NC}"
fi

# Test Vault connectivity
echo ""
echo "=== Testing Vault Connectivity ==="
if curl -s -f -o /dev/null "$VAULT_BASE_URL/v1/sys/health"; then
    echo -e "${GREEN}✓ Vault server is reachable${NC}"
else
    echo -e "${RED}✗ Cannot reach Vault server at $VAULT_BASE_URL${NC}"
    exit 1
fi

# Test token validity
echo ""
echo "=== Testing Token Validity ==="
TOKEN_LOOKUP=$(curl -s -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/auth/token/lookup-self")
if echo "$TOKEN_LOOKUP" | grep -q "errors"; then
    echo -e "${RED}✗ Token is invalid or expired${NC}"
    echo "Error response:"
    echo "$TOKEN_LOOKUP" | jq . 2>/dev/null || echo "$TOKEN_LOOKUP"
    exit 1
else
    echo -e "${GREEN}✓ Token is valid${NC}"
    echo "Token info:"
    echo "$TOKEN_LOOKUP" | jq '.data | {id: .id, policies: .policies, ttl: .ttl, expire_time: .expire_time}' 2>/dev/null || echo "$TOKEN_LOOKUP"
fi

# Test listing at different levels
echo ""
echo "=== Testing LIST Permissions ==="

# Test 1: List at engine root
echo ""
echo "1. Testing LIST at engine root ($VAULT_SECRET_ENGINE/):"
LIST_ROOT=$(curl -s -X LIST -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/$VAULT_SECRET_ENGINE/metadata/")
if echo "$LIST_ROOT" | grep -q "errors"; then
    echo -e "${YELLOW}⚠ Cannot list at engine root (this might be expected)${NC}"
    echo "$LIST_ROOT" | jq . 2>/dev/null || echo "$LIST_ROOT"
else
    echo -e "${GREEN}✓ Can list at engine root${NC}"
    echo "$LIST_ROOT" | jq . 2>/dev/null || echo "$LIST_ROOT"
fi

# Test 2: List at sigproject level
echo ""
echo "2. Testing LIST at sigproject/:"
LIST_SIGPROJECT=$(curl -s -X LIST -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/$VAULT_SECRET_ENGINE/metadata/sigproject/")
if echo "$LIST_SIGPROJECT" | grep -q "errors"; then
    echo -e "${RED}✗ Cannot list at sigproject/ path${NC}"
    echo "$LIST_SIGPROJECT" | jq . 2>/dev/null || echo "$LIST_SIGPROJECT"
else
    echo -e "${GREEN}✓ Can list at sigproject/ path${NC}"
    echo "$LIST_SIGPROJECT" | jq . 2>/dev/null || echo "$LIST_SIGPROJECT"
fi

# Test 3: List at specific path
echo ""
echo "3. Testing LIST at sigproject/tasklist:"
LIST_TASKLIST=$(curl -s -X LIST -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/$VAULT_SECRET_ENGINE/metadata/sigproject/tasklist")
if echo "$LIST_TASKLIST" | grep -q "errors"; then
    echo -e "${YELLOW}⚠ Cannot list at sigproject/tasklist (this is normal for a specific secret)${NC}"
else
    echo -e "${GREEN}✓ Can list at sigproject/tasklist${NC}"
    echo "$LIST_TASKLIST" | jq . 2>/dev/null || echo "$LIST_TASKLIST"
fi

# Test reading secrets
echo ""
echo "=== Testing READ Permissions ==="

# Test reading the secret
echo ""
echo "Testing READ at sigproject/tasklist:"
READ_SECRET=$(curl -s -H "X-Vault-Token: $VAULT_TOKEN" "$VAULT_BASE_URL/v1/$VAULT_SECRET_ENGINE/data/sigproject/tasklist")
if echo "$READ_SECRET" | grep -q "errors"; then
    echo -e "${RED}✗ Cannot read secret at sigproject/tasklist${NC}"
    echo "$READ_SECRET" | jq . 2>/dev/null || echo "$READ_SECRET"
else
    echo -e "${GREEN}✓ Can read secret at sigproject/tasklist${NC}"
    echo "Secret data (keys only):"
    echo "$READ_SECRET" | jq '.data.data | keys' 2>/dev/null || echo "Could not parse secret data"
fi

# Summary
echo ""
echo "=== Summary ==="
echo ""
echo "For Backstage Vault plugin to work, you need:"
echo "1. A valid Vault token"
echo "2. LIST permission on: $VAULT_SECRET_ENGINE/metadata/sigproject/"
echo "3. READ permission on: $VAULT_SECRET_ENGINE/data/sigproject/tasklist"
echo ""
echo "If permissions are missing, create a Vault policy with:"
echo ""
echo "path \"$VAULT_SECRET_ENGINE/metadata/sigproject/*\" {"
echo "  capabilities = [\"list\"]"
echo "}"
echo ""
echo "path \"$VAULT_SECRET_ENGINE/data/sigproject/*\" {"
echo "  capabilities = [\"read\"]"
echo "}"
echo ""
echo "Then create a token with that policy:"
echo "vault token create -policy=your-policy-name"