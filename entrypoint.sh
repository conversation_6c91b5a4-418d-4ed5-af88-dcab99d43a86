#!/bin/bash
set -euo pipefail

# Function to log messages
log() {
    echo "[entrypoint] $*" >&2
}

# Validate and merge NODE_OPTIONS
if [ -n "${NODE_OPTIONS_EXTRA:-}" ]; then
    log "Adding extra NODE_OPTIONS: $NODE_OPTIONS_EXTRA"

    # Check for conflicting options
    if [[ "$NODE_OPTIONS_EXTRA" == *"--no-node-snapshot"* ]] && [[ "${NODE_OPTIONS:-}" == *"--no-node-snapshot"* ]]; then
        log "Warning: --no-node-snapshot specified in both NODE_OPTIONS and NODE_OPTIONS_EXTRA"
    fi

    export NODE_OPTIONS="${NODE_OPTIONS_EXTRA} ${NODE_OPTIONS:-}"
    log "Final NODE_OPTIONS: $NODE_OPTIONS"
fi

# Optional: Validate configuration files exist
if [ "$1" = "node" ] && [ "$2" = "packages/backend" ]; then
    for config in "$@"; do
        if [[ "$config" == *.yaml ]] && [ ! -f "$config" ]; then
            log "Warning: Configuration file not found: $config"
        fi
    done
fi

# Execute the main command
exec "$@"