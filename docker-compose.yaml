version: '3'
services:
  backstage:
    # build:
    #   context: .
    #   dockerfile: packages/backend/Dockerfile
    image: backstage
    ports:
      - "7007:7007"
    depends_on:
      - postgres
    environment:
      POSTGRES_HOST: postgres
      POSTGRES_USER: dbadmin
      POSTGRES_PASSWORD: hes123Lo

  postgres:
    image: postgres:16
    environment:
      POSTGRES_USER: dbadmin
      POSTGRES_PASSWORD: hes123Lo
