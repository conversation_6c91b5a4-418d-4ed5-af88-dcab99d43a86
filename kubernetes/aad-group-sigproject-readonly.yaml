apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: aad-group-sigproject-readonly
  namespace: sigproject
  annotations:
    description: "Grants readonly privileges to AAD group EC-Backstage-team-sigproject"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
- apiGroup: rbac.authorization.k8s.io
  kind: Group
  name: "aad:d10c6a3b-143e-4e8d-b247-50c6a0196fb1"
