apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: aad-group-cluster-admin
  annotations:
    description: "Grants cluster-admin privileges to AAD group dce0b1a2-4250-4b0c-9ab7-c61e9a948699"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- apiGroup: rbac.authorization.k8s.io
  kind: Group
  name: "aad:dce0b1a2-4250-4b0c-9ab7-c61e9a948699"
