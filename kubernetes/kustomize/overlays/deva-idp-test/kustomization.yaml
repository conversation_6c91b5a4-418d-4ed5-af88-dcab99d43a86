apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: backstage

resources:
  - ../../base

patches:
  - target:
      kind: Ingress
      name: backstage
      namespace: backstage
    patch: |-
      - op: replace
        path: /spec/rules/0/host
        value: backstage.idp-test.deva.esc.esetrs.cz
      - op: replace
        path: /spec/tls/0/hosts/0
        value: backstage.idp-test.deva.esc.esetrs.cz
  - target:
      kind: Deployment
      name: backstage-app
      namespace: backstage
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/image
        value: escdev.azurecr.io/backstage/app:0.0.50
  - target:
      kind: Deployment
      name: backstage-app
      namespace: backstage
    patch: |-
      - op: add
        path: /spec/template/spec/hostAliases
        value:
          - ip: "**************"
            hostnames:
              - "gitlab.cluster.eset.corp"
  - target:
      kind: Deployment
      name: backstage-app
      namespace: backstage
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/env
        value:
        - name: LOG_LEVEL
          value: debug
        - name: NODE_OPTIONS_EXTRA
          value: "--inspect=0.0.0.0:9229"
  - target:
      kind: Deployment
      name: backstage-app
      namespace: backstage
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/ports/-
        value:
          name: debug
          containerPort: 9229

configMapGenerator:
  - name: backstage-config
    files:
      - ./config/app-config.yaml

secretGenerator:
  - name: backstage-secret
    literals:
      - AZURE_TENANT_ID=01f7e0e8-c680-4293-8068-d572231a88f4
#      - AZURE_CLIENT_ID=56c9f354-75c3-4b20-921a-748867f3fbb1
#      - AZURE_CLIENT_SECRET=****************************************
      - AZURE_CLIENT_ID=0322c71b-5074-4a65-b30e-72e7f57c36d7
      - AZURE_CLIENT_SECRET=****************************************
      - BACKSTAGE_BASE_URL=https://backstage.idp-test.deva.esc.esetrs.cz
      - GITLAB_TOKEN=************************** # Secret for catalog provider gitlabClusterEsetCorp.
      - VAULT_TOKEN=hvs.CAESIChhcs1AtjJUSRJ8bqxs-2IuceTE2-GBmTnW9cFE8Cw7Gh4KHGh2cy4yU1hqNlNzaVBmOEFIN0pNdlljZDlJdTQ
      - VAULT_BASE_URL=https://vault.hub.deva.esc.esetrs.cz
      - VAULT_SECRET_ENGINE=dev-secrets
      - VAULT_KV_VERSION=2
      - DEV_HUB_K8S_TOKEN="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      - DEV_IDP_TEST_K8S_TOKEN="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      - ARGOCD_URL_DAVA_HUB=https://argocd.hub.deva.esc.esetrs.cz
      - ARGOCD_AUTH_TOKEN_DAVA_HUB="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.umvt7kULMFgp8ut5hD9jKSuER1SBbpciqyJ0E9NSDoU"
      - TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME=backstagetechdocsesc
      - TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY="****************************************************************************************"
      - SESSION_SECRET=I+KQg5GNbc8LBfTHtN30gWUAxIUaVo8R
  - name: postgres-secret
    literals:
      - POSTGRES_HOST=postgres-svc
      - POSTGRES_PORT=5432
      - POSTGRES_USER=backstage
      - POSTGRES_PASSWORD=Ht8uPxu&59Pi

generatorOptions:
  disableNameSuffixHash: true
