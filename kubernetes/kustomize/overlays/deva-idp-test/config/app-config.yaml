app:
  title: Eset Backstage
  baseUrl: ${BACKSTAGE_BASE_URL}

backend:
  baseUrl: ${BACKSTAGE_BASE_URL}
  listen: ':7007'
  cors:
    origin: ${BACKSTAGE_BASE_URL}
  database:
    client: pg
    connection:
      host: ${POSTGRES_HOST}
      port: ${POSTGRES_PORT}
      user: ${POSTGRES_USER}
      password: ${POSTGRES_PASSWORD}
  auth:
    externalAccess:
      - type: legacy
        options:
          secret: Y25Qc2ttY0pQTFp4UnpRQVdPSW1uejZ0VXQzTFdHWTVQbTA1a3NxcAo=
          subject: legacy-backend-auth-key
#auth:
#  environment: production
#  providers:
#    guest:
#      userEntityRef: user:default/MemberFoo
#      dangerouslyAllowOutsideDevelopment: true
#    microsoft:
#      production:
#        clientId: ${AZURE_CLIENT_ID}
#        clientSecret: ${AZURE_CLIENT_SECRET}
#        tenantId: ${AZURE_TENANT_ID}
#        domainHint: ${AZURE_TENANT_ID}
#        signIn:
#          resolvers:
#            - resolver: userIdMatchingUserEntityAnnotation

auth:
  environment: production
  session:
    secret: ${SESSION_SECRET}
  providers:
    guest:
      userEntityRef: user:default/MemberFoo
      dangerouslyAllowOutsideDevelopment: true
    microsoft:
      production:
        clientId: ${AZURE_CLIENT_ID}
        clientSecret: ${AZURE_CLIENT_SECRET}
        tenantId: ${AZURE_TENANT_ID}
        domainHint: ${AZURE_TENANT_ID}
        additionalScopes:
          - "openid"         # For ID tokens
          - "profile"        # For user profile
          - "email"          # For email claim
        signIn:
          resolvers:
            # Change to email resolver for better Kubernetes username mapping
            - resolver: emailMatchingUserEntityProfileEmail

kubernetes:
  serviceLocatorMethod:
    type: multiTenant
  clusterLocatorMethods:
    - type: catalog

#kubernetes:
#  serviceLocatorMethod:
#    type: 'multiTenant'
#  clusterLocatorMethods:
#    - type: 'config'
#      clusters:
#        - name: idp-test
#          url: https://api.idp-test.deva.esc.esetrs.cz:6443
#          authProvider: 'oidc'
#          oidcTokenProvider: 'microsoft'  # Must match your auth provider name
#          skipTLSVerify: true  # As per your current config
#          skipMetricsLookup: true
#          caData: ${K8S_CA_DATA}  # Or use caFile

#kubernetes:
#  serviceLocatorMethod:
#    type: 'multiTenant'
#  clusterLocatorMethods:
#    - type: 'config'
#      clusters:
#        - url: https://api.hub.deva.esc.esetrs.cz:6443
#          name: DEVA HUB
#          authProvider: 'serviceAccount'
#          skipTLSVerify: true
#          skipMetricsLookup: true
#          serviceAccountToken: ${DEV_HUB_K8S_TOKEN}
#        - url: https://api.idp-test.deva.esc.esetrs.cz:6443
#          name: idp-test
#          authProvider: 'serviceAccount'
#          skipTLSVerify: true
#          skipMetricsLookup: true
#          serviceAccountToken: ${DEV_IDP_TEST_K8S_TOKEN}


argocd:
  appLocatorMethods:
    - type: 'config'
      instances:
        - name: argo-deva-hub
          url: ${ARGOCD_URL_DAVA_HUB}
          token: ${ARGOCD_AUTH_TOKEN_DAVA_HUB} # Token to use to instance