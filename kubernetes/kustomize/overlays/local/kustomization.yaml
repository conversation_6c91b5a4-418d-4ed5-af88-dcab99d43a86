apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: backstage

resources:
  - ../../base
  - postgres-pv.yaml
  - testing-pod.yaml

patches:
  - path: change-postgres-pvc-class.yaml
  - target:
      kind: Deployment
      name: backstage-app
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/imagePullPolicy
        value: Never


# section to override the base image
images:
  - name: escdev.azurecr.io/backstage/app
    newTag: 1.0.34-20250224171421
    newName: backstage



configMapGenerator:
  - name: backstage-config
    files:
      - ./config/app-config.yaml

secretGenerator:
  - name: backstage-secret
    literals:
      - AZURE_TENANT_ID=01f7e0e8-c680-4293-8068-d572231a88f4
      - AZURE_CLIENT_ID=56c9f354-75c3-4b20-921a-748867f3fbb1
      - AZURE_CLIENT_SECRET=****************************************
      - GITHUB_TOKEN=ghp_1
      - K8S_MINIKUBE_TOKEN="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      - GITLAB_TOKEN=**************************
      - VAULT_TOKEN=hvs.CAESIChhcs1AtjJUSRJ8bqxs-2IuceTE2-GBmTnW9cFE8Cw7Gh4KHGh2cy4yU1hqNlNzaVBmOEFIN0pNdlljZDlJdTQ
      - VAULT_BASE_URL=https://vault.hub.deva.esc.esetrs.cz
      - VAULT_SECRET_ENGINE=dev-secrets
      - VAULT_KV_VERSION=2
      - ARGOCD_URL_DAVA_HUB=https://argocd.hub.deva.esc.esetrs.cz
      - ARGOCD_AUTH_TOKEN_DAVA_HUB="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.iUDFNQPtCp-rdONgm9CyLeFniR_1ll_gr2hOoODgjaM"
  - name: postgres-secret
    literals:
      - POSTGRES_HOST=postgres-svc
      - POSTGRES_PORT=5432
      - POSTGRES_USER=backstage
      - POSTGRES_PASSWORD=gWLn5vk%p*mL

generatorOptions:
  disableNameSuffixHash: true