app:
  baseUrl: http://localhost:3000
backend:
  baseUrl: http://localhost:3000
  listen: ':7007'
  cors:
    origin: http://localhost:3000
  database:
    client: pg
    connection:
      host: ${POSTGRES_HOST}
      port: ${POSTGRES_PORT}
      user: ${POSTGRES_USER}
      password: ${POSTGRES_PASSWORD}
  auth:
    externalAccess:
      - type: legacy
        options:
          secret: Y25Qc2ttY0pQTFp4UnpRQVdPSW1uejZ0VXQzTFdHWTVQbTA1a3NxcAo=
          subject: legacy-backend-auth-key
auth:
  environment: development
  providers:
    microsoft:
      development:
        clientId: ${AZURE_CLIENT_ID}
        clientSecret: ${AZURE_CLIENT_SECRET}
        tenantId: ${AZURE_TENANT_ID}
        domainHint: ${AZURE_TENANT_ID}
        signIn:
          resolvers:
            - resolver: userIdMatchingUserEntityAnnotation
kubernetes:
  serviceLocatorMethod:
    type: 'multiTenant'
  clusterLocatorMethods:
    - type: 'config'
      clusters:
        - url: https://kubernetes.default.svc
          name: minikube
          authProvider: 'serviceAccount'
          skipTLSVerify: true
          skipMetricsLookup: true
          serviceAccountToken: ${IN_CLUSTER_K8S_TOKEN}

# Need to change root domain to corp
integrations:
  gitlab:
    - host: gitlab.cluster.eset.corp
      apiBaseUrl: https://gitlab.cluster.eset.corp/api/v4
      token: ${GITLAB_TOKEN}

catalog:
  providers:
    gitlab:
      gitlabClusterEsetCorp:
        host: gitlab.cluster.eset.corp
        branch: main
        fallbackBranch: master
        skipForkedRepos: false
        includeArchivedRepos: false
        group: SigProject
        entityFilename: catalog-info.yaml
        projectPattern: '[\s\S]*'
        excludeRepos: []
        schedule:
          frequency: { minutes: 30 }
          timeout: { minutes: 3 }
  locations:
    # Local example data, replace this with your production config, these are intended for demo use only.
    # File locations are relative to the backend process, typically in a deployed context, such as in a Docker container, this will be the root
    - type: file
      target: ./default/groups.yaml
      rules:
        - allow: [ Group ]

    - type: file
      target: ./examples/example-entities.yaml
      rules:
        - allow: [ System, Component, API, Resource, Domain, Location ]

    - type: url
      target: https://gitlab.cluster.eset.corp/sigproject/backstage-templates/-/blob/main/kafka-separated/template.yaml
      rules:
        - allow: [ Template ]

    - type: file
      target: ./examples/eset-org.yaml
      rules:
        - allow: [ User, Group ]

    - type: file
      target: ./examples/cloudfield-org.yaml
      rules:
        - allow: [ User, Group ]

    # Local example template
    - type: file
      target: ./examples/template/template.yaml
      rules:
        - allow: [ Template ]


    - type: file
      target: ./examples/foo-org.yaml
      rules:
        - allow: [ User, Group ]

    - type: file
      target: ./examples/foo-entities.yaml
      rules:
        - allow: [ System, Component, API, Resource, Domain, Location ]

argocd:
  appLocatorMethods:
    - type: 'config'
      instances:
        - name: argo-deva-hub
          url: ${ARGOCD_URL_DAVA_HUB}
          token: ${ARGOCD_AUTH_TOKEN_DAVA_HUB} # Token to use to instance