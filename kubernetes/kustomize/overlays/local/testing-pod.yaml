---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-service
  labels:
    app: my-service
    backstage.io/kubernetes-id: my-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: my-service
  template:
    metadata:
      labels:
        app: my-service
        backstage.io/kubernetes-id: my-service
    spec:
      serviceAccountName: backstage
      containers:
        - name: nginx
          image: docker.io/nginx:1.14.2
          ports:
            - containerPort: 80
        - name: netshoot
          image: docker.io/nicolaka/netshoot
          command: ["/bin/bash"]
          args: ["-c", "while true; do ping localhost; sleep 60;done"]
---
apiVersion: v1
kind: Service
metadata:
  name: my-service
  labels:
    backstage.io/kubernetes-id: my-service
spec:
  selector:
    app: my-service
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
