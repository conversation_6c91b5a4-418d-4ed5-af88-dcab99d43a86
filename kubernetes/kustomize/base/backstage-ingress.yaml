apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backstage
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
spec:
  ingressClassName: ec-platform
  rules:
      - host: backstage.eset.cz
        http:
          paths:
            - path: /
              pathType: Prefix
              backend:
                service:
                  name: backstage-svc
                  port:
                    number: 7007
  tls:
    - hosts:
        - backstage.eset.cz
      secretName: backstage-app-tls