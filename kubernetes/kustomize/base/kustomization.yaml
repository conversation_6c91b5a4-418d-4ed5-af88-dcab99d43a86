apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: backstage

resources:
  - backstage-namespace.yaml
  - backstage-deployment.yaml
  - backstage-service.yaml
  - backstage-ingress.yaml
  - postgres-deployment.yaml
  - postgres-pvc.yaml
  - postgres-service.yaml
  - backstage-serviceaccount.yaml

secretGenerator:
  - name: backstage-escdev-pull-secret
    type: kubernetes.io/dockerconfigjson
    files:
      - .dockerconfigjson=./secrets/escdev-docker-config.json

generatorOptions:
  disableNameSuffixHash: true