apiVersion: apps/v1
kind: Deployment
metadata:
  name: backstage-postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backstage-postgres
  template:
    metadata:
      labels:
        app: backstage-postgres
    spec:
      containers:
        - name: postgres
          image: docker.io/postgres:16.6-alpine
          imagePullPolicy: IfNotPresent
          ports:
            - name: pg-db-port
              containerPort: 5432
          envFrom:
            - secretRef:
                name: postgres-secret
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: pgdata
              subPath: pgdata
      volumes:
        - name: pgdata
          persistentVolumeClaim:
            claimName: postgres-pvc