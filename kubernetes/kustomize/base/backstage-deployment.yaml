apiVersion: apps/v1
kind: Deployment
metadata:
  name: backstage-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backstage-app
  template:
    metadata:
      labels:
        app: backstage-app
    spec:
      serviceAccountName: backstage
      containers:
        - name: backstage
          image: escdev.azurecr.io/backstage/app:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 7007
          readinessProbe:
            httpGet:
              port: 7007
              path: /.backstage/health/v1/readiness
            initialDelaySeconds: 5
          livenessProbe:
            httpGet:
              port: 7007
              path: /.backstage/health/v1/liveness
          envFrom:
            - secretRef:
                name: postgres-secret
            - secretRef:
                name: backstage-secret
          volumeMounts:
            - name: app-config
              mountPath: /app/configuration/app-config.yaml
              subPath: app-config.yaml
      volumes:
        - name: app-config
          configMap:
            name: backstage-config
      imagePullSecrets:
        - name: backstage-escdev-pull-secret