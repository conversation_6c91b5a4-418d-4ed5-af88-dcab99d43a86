p, role:default/system_admin, catalog.entity.read, read, allow
p, role:default/system_admin, catalog.entity.create, create, allow
p, role:default/system_admin, catalog.entity.refresh, update, allow
p, role:default/system_admin, catalog.entity.delete, delete, allow
p, role:default/system_admin, catalog.location.read, read, allow
p, role:default/system_admin, catalog.location.create, create, allow
p, role:default/system_admin, catalog.location.delete, delete, allow
p, role:default/system_admin, scaffolder.action.execute, execute, allow
p, role:default/system_admin, scaffolder.template.parameter.read, read, allow
p, role:default/system_admin, scaffolder.template.step.read, read, allow
p, role:default/system_admin, scaffolder.template, create, allow
p, role:default/system_admin, scaffolder-action, use, allow
p, role:default/system_admin, scaffolder.task.read, read, allow
p, role:default/system_admin, scaffolder.task.create, create, allow
p, role:default/system_admin, scaffolder.template.management, use, allow
p, role:default/system_admin, scaffolder.task.cancel, use, allow
p, role:default/system_admin, scaffolder-template, read, allow
p, role:default/system_admin, kubernetes.proxy, use, allow

g, group:default/backstage_system_admin, role:default/system_admin

